# GameFlex AWS Backend Environment Configuration Example
# Copy this file to .env and update the values as needed

############
# AWS Configuration
############
AWS_DEFAULT_REGION=us-east-1
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_ENDPOINT_URL=http://localhost:4566

############
# LocalStack Pro Configuration
############
LOCALSTACK_HOST=localhost:4566
LOCALSTACK_HOSTNAME=localhost
DEBUG=1
PERSISTENCE=1

# LocalStack Pro Auth Token (required for Pro features)
# Get your token from: https://app.localstack.cloud/
LOCALSTACK_AUTH_TOKEN=your-localstack-pro-auth-token-here

# Docker container name
LOCALSTACK_DOCKER_NAME=gameflex-localstack

# Volume directory for persistence
LOCALSTACK_VOLUME_DIR=./volume

############
# Cognito Configuration (Auto-generated during initialization)
############
# These values are automatically populated when AWS services are initialized
COGNITO_USER_POOL_ID=
COGNITO_USER_POOL_CLIENT_ID=

############
# DynamoDB Configuration (LocalStack)
############
# DynamoDB configuration for LocalStack
DYNAMODB_ENDPOINT_URL=http://localhost:4566
DYNAMODB_REGION=us-east-1
DYNAMODB_ACCESS_KEY_ID=test
DYNAMODB_SECRET_ACCESS_KEY=test

# DynamoDB Table Names
DYNAMODB_TABLE_USERS=Users
DYNAMODB_TABLE_USER_PROFILES=UserProfiles
DYNAMODB_TABLE_CHANNELS=Channels
DYNAMODB_TABLE_CHANNEL_MEMBERS=ChannelMembers
DYNAMODB_TABLE_MEDIA=Media
DYNAMODB_TABLE_POSTS=Posts
DYNAMODB_TABLE_COMMENTS=Comments
DYNAMODB_TABLE_LIKES=Likes
DYNAMODB_TABLE_FOLLOWS=Follows
DYNAMODB_TABLE_NOTIFICATIONS=Notifications

############
# Cognito Configuration
############
COGNITO_USER_POOL_NAME=gameflex-users
COGNITO_USER_POOL_CLIENT_NAME=gameflex-client
COGNITO_IDENTITY_POOL_NAME=gameflex-identity

# These will be populated after Cognito setup
COGNITO_USER_POOL_ID=
COGNITO_USER_POOL_CLIENT_ID=
COGNITO_IDENTITY_POOL_ID=

############
# S3 Configuration
############
S3_BUCKET_MEDIA=gameflex-media
S3_BUCKET_AVATARS=gameflex-avatars
S3_BUCKET_TEMP=gameflex-temp

############
# API Gateway Configuration
############
API_GATEWAY_NAME=gameflex-api
API_GATEWAY_STAGE=dev
API_GATEWAY_DESCRIPTION=GameFlex API Gateway

############
# Lambda Configuration
############
LAMBDA_RUNTIME=python3.11
LAMBDA_TIMEOUT=30
LAMBDA_MEMORY_SIZE=256

############
# Application Configuration
############
APP_NAME=GameFlex
APP_VERSION=1.0.0
APP_ENVIRONMENT=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-token-with-at-least-32-characters-long
JWT_EXPIRY=3600
JWT_REFRESH_EXPIRY=604800

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://********:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token

############
# File Upload Configuration
############
MAX_FILE_SIZE=52428800  # 50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mov,avi
UPLOAD_PATH=uploads

############
# Email Configuration (for Cognito)
############
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

############
# Logging Configuration
############
LOG_LEVEL=DEBUG
LOG_FORMAT=json

############
# Security Configuration
############
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

############
# Development Configuration
############
ENABLE_DEBUG_LOGS=true
ENABLE_CORS=true
ENABLE_SWAGGER=true
