import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/aws_auth_service.dart';
import 'package:gameflex_mobile/services/config_service.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';
import 'package:gameflex_mobile/providers/posts_provider.dart';

void main() {
  group('AWS Backend Integration Tests', () {
    setUpAll(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    test('should access AWS services without errors', () {
      print('🔧 Testing AWS service access...');

      // Test that services can be instantiated
      final configService = ConfigService.instance;
      final authService = AwsAuthService.instance;
      final authProvider = AuthProvider();
      final postsProvider = PostsProvider();

      expect(configService, isNotNull);
      expect(authService, isNotNull);
      expect(authProvider, isNotNull);
      expect(postsProvider, isNotNull);

      print('✅ All AWS services accessible');
    });

    test('should have correct environment configuration', () {
      print('🌍 Testing environment configuration...');

      final configService = ConfigService.instance;

      // Test environment detection
      final environmentName = configService.getEnvironmentName();
      final isDev = configService.isDevelopment;
      final isStaging = configService.isStaging;
      final isProduction = configService.isProduction;

      expect(environmentName, isNotNull);
      expect(
        ['Development', 'Staging', 'Production'].contains(environmentName),
        isTrue,
      );

      // Exactly one environment should be true
      final trueCount = [isDev, isStaging, isProduction].where((x) => x).length;
      expect(trueCount, equals(1));

      print('✅ Environment: $environmentName');
      print('   Development: $isDev');
      print('   Staging: $isStaging');
      print('   Production: $isProduction');
    });

    test('should have server URL options available', () {
      print('🌐 Testing server URL configuration...');

      final configService = ConfigService.instance;
      final options = configService.getServerUrlOptions();

      expect(options, isNotEmpty);

      // Check for required URL options
      final hasLocalhost = options.any(
        (option) => option.url.contains('localhost'),
      );
      final hasGameflex = options.any(
        (option) => option.url.contains('gameflex.io'),
      );

      expect(
        hasLocalhost,
        isTrue,
        reason: 'Should have localhost option for development',
      );
      expect(
        hasGameflex,
        isTrue,
        reason: 'Should have gameflex.io option for production',
      );

      print('✅ Available server options:');
      for (final option in options) {
        print('   - ${option.name}: ${option.url}');
      }
    });

    test('should initialize auth provider without errors', () {
      print('🔐 Testing auth provider initialization...');

      final authProvider = AuthProvider();

      expect(authProvider.isAuthenticated, isFalse);
      expect(authProvider.user, isNull);
      // Status might be loading if auth check is in progress, or initial
      expect(
        [AuthStatus.initial, AuthStatus.loading].contains(authProvider.status),
        isTrue,
      );

      print('✅ Auth provider initialized correctly');
      print('   Authenticated: ${authProvider.isAuthenticated}');
      print('   Status: ${authProvider.status}');
    });

    test('should initialize posts provider without errors', () {
      print('📝 Testing posts provider initialization...');

      final postsProvider = PostsProvider();

      expect(postsProvider.posts, isEmpty);
      expect(postsProvider.status, equals(PostsStatus.initial));
      expect(postsProvider.hasMore, isTrue);

      print('✅ Posts provider initialized correctly');
      print('   Posts count: ${postsProvider.posts.length}');
      print('   Status: ${postsProvider.status}');
      print('   Has more: ${postsProvider.hasMore}');
    });

    test('should handle AWS auth service state', () {
      print('🔑 Testing AWS auth service state...');

      final authService = AwsAuthService.instance;

      expect(authService.isAuthenticated, isFalse);
      expect(authService.currentUser, isNull);

      print('✅ AWS auth service state correct');
      print('   Authenticated: ${authService.isAuthenticated}');
      print('   Current user: ${authService.currentUser}');
    });

    test('should provide debug information', () async {
      print('🐛 Testing debug information...');

      final configService = ConfigService.instance;

      try {
        final debugInfo = await configService.getDebugInfo();

        expect(debugInfo, isA<Map<String, dynamic>>());
        expect(debugInfo['environment'], isNotNull);
        expect(debugInfo['platform'], isNotNull);

        print('✅ Debug information available:');
        debugInfo.forEach((key, value) {
          print('   $key: $value');
        });
      } catch (e) {
        print('ℹ️  Debug info not available in test environment: $e');
        // This is acceptable in test environment
      }
    });
  });
}
