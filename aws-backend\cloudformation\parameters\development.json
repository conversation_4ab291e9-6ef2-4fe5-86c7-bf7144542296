[{"ParameterKey": "Environment", "ParameterValue": "development"}, {"ParameterKey": "ProjectName", "ParameterValue": "gameflex"}, {"ParameterKey": "DomainName", "ParameterValue": "localhost"}, {"ParameterKey": "ApiDomainName", "ParameterValue": "localhost"}, {"ParameterKey": "CertificateArn", "ParameterValue": ""}, {"ParameterKey": "EnableCloudFront", "ParameterValue": "false"}, {"ParameterKey": "EnableWAF", "ParameterValue": "false"}, {"ParameterKey": "EnableXRay", "ParameterValue": "false"}, {"ParameterKey": "LambdaMemorySize", "ParameterValue": "256"}, {"ParameterKey": "LambdaTimeout", "ParameterValue": "30"}, {"ParameterKey": "S3BucketVersioning", "ParameterValue": "Suspended"}, {"ParameterKey": "S3BucketEncryption", "ParameterValue": "false"}, {"ParameterKey": "EnableS3AccessLogging", "ParameterValue": "false"}, {"ParameterKey": "CognitoPasswordMinLength", "ParameterValue": "8"}, {"ParameterKey": "CognitoPasswordRequireUppercase", "ParameterValue": "true"}, {"ParameterKey": "CognitoPasswordRequireLowercase", "ParameterValue": "true"}, {"ParameterKey": "CognitoPasswordRequireNumbers", "ParameterValue": "true"}, {"ParameterKey": "CognitoPasswordRequireSymbols", "ParameterValue": "false"}, {"ParameterKey": "CognitoMfaConfiguration", "ParameterValue": "OFF"}, {"ParameterKey": "ApiGatewayThrottleBurstLimit", "ParameterValue": "200"}, {"ParameterKey": "ApiGatewayThrottleRateLimit", "ParameterValue": "100"}, {"ParameterKey": "EnableApiGatewayLogging", "ParameterValue": "false"}, {"ParameterKey": "ApiGatewayLogLevel", "ParameterValue": "INFO"}, {"ParameterKey": "EnableDetailedMetrics", "ParameterValue": "false"}, {"ParameterKey": "AlertingEmail", "ParameterValue": "<EMAIL>"}, {"ParameterKey": "Enable<PERSON><PERSON><PERSON>", "ParameterValue": "false"}, {"ParameterKey": "LogRetentionDays", "ParameterValue": "7"}, {"ParameterKey": "EnableVPCEndpoints", "ParameterValue": "false"}, {"ParameterKey": "VPCCidr", "ParameterValue": "10.0.0.0/16"}, {"ParameterKey": "PublicSubnet1Cidr", "ParameterValue": "10.0.1.0/24"}, {"ParameterKey": "PublicSubnet2Cidr", "ParameterValue": "10.0.2.0/24"}, {"ParameterKey": "PrivateSubnet1Cidr", "ParameterValue": "10.0.3.0/24"}, {"ParameterKey": "PrivateSubnet2Cidr", "ParameterValue": "10.0.4.0/24"}]