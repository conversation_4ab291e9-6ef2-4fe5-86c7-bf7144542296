# AWS Backend Integration Test Plan

## Prerequisites
1. ✅ AWS Backend is running (LocalStack on port 4566)
2. ✅ Flutter app is running on Windows
3. ✅ Test users are available (<EMAIL>, <EMAIL>)

## Test Results Summary

### ✅ App Launch and Build
- **Status**: PASSED
- **Details**: App compiles and launches successfully on Windows
- **Notes**: Minor UI overflow in dev server dropdown (cosmetic issue)

### ⚠️ Configuration Service
- **Status**: PARTIAL
- **Details**: 
  - App shows dev server dropdown
  - Currently still connecting to Supabase (localhost:8090)
  - Need to verify AWS backend URL switching works
- **Action Required**: Test server URL switching in the app

### 🔄 Authentication Testing
- **Status**: PENDING
- **Test Steps**:
  1. Open the app
  2. Navigate to login screen
  3. Try to sign <NAME_EMAIL> / DevPassword123!
  4. Verify authentication works with AWS Cognito
  5. Test sign out functionality

### 🔄 Posts Functionality
- **Status**: PENDING  
- **Test Steps**:
  1. After successful login
  2. Navigate to home/feed screen
  3. Try to load posts from AWS backend
  4. Test creating a new post
  5. Test liking/unliking posts

### 🔄 Media Upload
- **Status**: PENDING
- **Test Steps**:
  1. Try to upload an image
  2. Verify image is stored in S3 bucket
  3. Verify image appears in posts

### ⚠️ User Profiles
- **Status**: NOT IMPLEMENTED
- **Details**: User profile screen shows "Feature Not Implemented" message
- **Expected**: This is correct behavior as user profiles are not implemented in AWS backend yet

### ⚠️ Channels
- **Status**: NOT IMPLEMENTED  
- **Details**: Channels functionality removed for AWS backend migration
- **Expected**: This is correct behavior as channels are not implemented in AWS backend yet

## Manual Testing Instructions

### Test 1: Server URL Configuration
1. Look for the dev server dropdown in the app
2. Try switching between different server URLs:
   - Auto-detect
   - Localhost (http://localhost:45660)
   - Android Emulator (http://********:45660)
   - Staging (http://dev.api.gameflex.io:8000)
   - Production (https://api.gameflex.io)
3. Verify the selection is saved and persisted

### Test 2: AWS Authentication
1. Set server URL to "Localhost" (http://localhost:45660)
2. Navigate to login screen
3. Enter credentials:
   - Email: <EMAIL>
   - Password: DevPassword123!
4. Tap "Sign In"
5. Expected: Successful authentication with AWS Cognito
6. Verify user is logged in and can access authenticated features
7. Test sign out functionality

### Test 3: Posts Integration
1. After successful login
2. Navigate to home/feed screen
3. Expected: Posts load from AWS DynamoDB
4. Try creating a new post:
   - Tap create post button
   - Enter some text content
   - Submit the post
5. Expected: Post is created and appears in feed
6. Try liking a post
7. Expected: Like count updates

### Test 4: Error Handling
1. Set server URL to an invalid URL
2. Try to authenticate
3. Expected: Appropriate error messages
4. Set server URL back to localhost
5. Try authentication again
6. Expected: Works correctly

## Expected Behavior

### Working Features ✅
- App compilation and launch
- UI navigation
- Configuration service (basic functionality)
- User profile stub screen
- Dev server dropdown

### AWS Backend Features 🔄
- Authentication with Cognito
- Posts CRUD operations
- Media upload to S3
- Error handling and logging

### Not Implemented ⚠️
- User profiles (intentionally not implemented)
- Channels (intentionally removed)
- Real-time features
- Advanced media processing

## Test Environment
- **Platform**: Windows 11
- **Flutter Version**: Latest stable
- **Backend**: AWS LocalStack on port 4566
- **Database**: DynamoDB (via LocalStack)
- **Storage**: S3 (via LocalStack)
- **Authentication**: Cognito (via LocalStack)

## Notes
- Supabase connection errors are expected as we're migrating away from Supabase
- Minor UI overflow in dropdown is cosmetic and doesn't affect functionality
- All AWS services are running locally via LocalStack for testing
