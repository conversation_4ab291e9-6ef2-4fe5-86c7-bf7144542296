# GameFlex AWS Backend Environment Configuration - QA
# This file contains QA-specific environment variables

############
# Environment Configuration
############
APP_ENVIRONMENT=qa
NODE_ENV=production
DEBUG=true

############
# AWS Configuration - Real AWS
############
AWS_DEFAULT_REGION=us-east-1
# AWS credentials should be configured via AWS CLI profiles
# or IAM roles, not stored in this file
AWS_PROFILE=qa

############
# DynamoDB Configuration
############
# DynamoDB configuration for QA environment
DYNAMODB_REGION=us-east-1
# DynamoDB credentials should be configured via AWS CLI profiles
# or IAM roles, not stored in this file

# DynamoDB Table Names (with environment prefix)
DYNAMODB_TABLE_USERS=gameflex-qa-Users
DYNAMODB_TABLE_USER_PROFILES=gameflex-qa-UserProfiles
DYNAMODB_TABLE_CHANNELS=gameflex-qa-Channels
DYNAMODB_TABLE_CHANNEL_MEMBERS=gameflex-qa-ChannelMembers
DYNAMODB_TABLE_MEDIA=gameflex-qa-Media
DYNAMODB_TABLE_POSTS=gameflex-qa-Posts
DYNAMODB_TABLE_COMMENTS=gameflex-qa-Comments
DYNAMODB_TABLE_LIKES=gameflex-qa-Likes
DYNAMODB_TABLE_FOLLOWS=gameflex-qa-Follows
DYNAMODB_TABLE_NOTIFICATIONS=gameflex-qa-Notifications

############
# Cognito Configuration (Populated from CloudFormation outputs)
############
COGNITO_USER_POOL_ID=
COGNITO_USER_POOL_CLIENT_ID=
COGNITO_IDENTITY_POOL_ID=

############
# S3 Configuration
############
S3_BUCKET_MEDIA=gameflex-media-qa
S3_BUCKET_AVATARS=gameflex-avatars-qa
S3_BUCKET_TEMP=gameflex-temp-qa

############
# API Gateway Configuration
############
API_GATEWAY_URL=https://qa.api.gameflex.io
API_GATEWAY_STAGE=qa

############
# Lambda Configuration
############
LAMBDA_RUNTIME=nodejs18.x
LAMBDA_TIMEOUT=30
LAMBDA_MEMORY_SIZE=512

############
# Application Configuration
############
APP_NAME=GameFlex
APP_VERSION=1.0.0
PROJECT_NAME=gameflex

# JWT Configuration
JWT_SECRET=
JWT_EXPIRY=3600
JWT_REFRESH_EXPIRY=604800

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://qa.gameflex.io,http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token

############
# File Upload Configuration
############
MAX_FILE_SIZE=52428800  # 50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mov,avi
UPLOAD_PATH=uploads

############
# Email Configuration (for Cognito)
############
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

############
# Logging Configuration
############
LOG_LEVEL=INFO
LOG_FORMAT=json
ENABLE_DEBUG_LOGS=true

############
# Security Configuration
############
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

############
# QA Configuration
############
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_HOT_RELOAD=false
ENABLE_MOCK_DATA=false

############
# Monitoring and Alerting
############
ENABLE_METRICS=true
ENABLE_TRACING=true
ENABLE_ALERTS=true
ALERTING_EMAIL=<EMAIL>

############
# Performance Configuration
############
ENABLE_CACHING=true
CACHE_TTL=300
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=1000

############
# Feature Flags
############
FEATURE_USER_REGISTRATION=true
FEATURE_SOCIAL_LOGIN=true
FEATURE_PUSH_NOTIFICATIONS=true
FEATURE_ANALYTICS=true
