# GameFlex AWS Backend Startup Script (PowerShell)
# This script starts the AWS backend using LocalStack Pro with Docker-based initialization

param(
    [switch]$Force,
    [switch]$Verbose,
    [switch]$BuildOnly,
    [switch]$SkipBuild
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Load environment variables from .env file
function Load-EnvFile {
    if (Test-Path ".env") {
        Write-Status "Loading environment variables from .env file..."
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^\s*([^#][^=]*)\s*=\s*(.*)\s*$") {
                $name = $matches[1].Trim()
                $value = $matches[2].Trim()
                # Remove quotes if present
                $value = $value -replace '^"(.*)"$', '$1'
                $value = $value -replace "^'(.*)'$", '$1'
                [Environment]::SetEnvironmentVariable($name, $value, "Process")
                if ($Verbose) {
                    Write-Host "  Set $name" -ForegroundColor Gray
                }
            }
        }
    }
    else {
        Write-Warning ".env file not found. Using default values."
    }
}

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[GAMEFLEX] $Message" -ForegroundColor Blue
}

# Check if Docker Desktop is running
function Test-Docker {
    try {
        docker info | Out-Null
        Write-Status "Docker Desktop is running"
        return $true
    }
    catch {
        Write-Error "Docker Desktop is not running. Please start Docker Desktop and try again."
        return $false
    }
}

# Check if required ports are available
function Test-Ports {
    $ports = @(4566, 45660)  # Only LocalStack ports needed
    $portsInUse = @()

    foreach ($port in $ports) {
        $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connection) {
            $portsInUse += $port
            Write-Warning "Port $port is already in use"
        }
    }

    if ($portsInUse.Count -gt 0 -and -not $Force) {
        $response = Read-Host "Do you want to continue anyway? (y/N)"
        if ($response -notmatch "^[Yy]$") {
            Write-Error "Startup cancelled"
            return $false
        }
    }

    Write-Status "Port check completed"
    return $true
}

# Create necessary directories
function New-Directories {
    Write-Status "Creating necessary directories..."
    
    $directories = @(
        "init",
        "lambda-functions",
        "cloudformation", 
        "database\init",
        "logs"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Status "Directories created"
}

# Start Docker services
function Start-Services {
    Write-Header "Starting GameFlex AWS Backend..."

    try {
        # Pull latest images
        Write-Status "Pulling latest Docker images..."
        docker compose pull

        # Start LocalStack service first
        Write-Status "Starting LocalStack service..."
        docker compose up -d localstack

        # Wait for LocalStack to be healthy using Docker health check
        Write-Status "Waiting for LocalStack container to be healthy..."
        $timeout = 180
        $counter = 0

        do {
            $healthStatus = docker inspect gameflex-localstack --format='{{.State.Health.Status}}' 2>$null
            if ($healthStatus -eq "healthy") {
                Write-Status "LocalStack container is healthy"
                break
            }

            Start-Sleep -Seconds 3
            $counter += 3

            if ($counter -ge $timeout) {
                Write-Error "LocalStack failed to become healthy within $timeout seconds"
                Write-Status "Checking LocalStack logs..."
                docker compose logs localstack --tail 20
                throw "LocalStack health check timeout"
            }

            if ($counter % 15 -eq 0) {
                Write-Status "Still waiting for LocalStack... ($counter/$timeout seconds)"
            }
        } while ($true)

        # Additional wait for LocalStack services to be fully ready
        Write-Status "Verifying LocalStack services are ready..."
        $serviceTimeout = 60
        $serviceCounter = 0

        do {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:45660/_localstack/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    $healthData = $response.Content | ConvertFrom-Json
                    $allServicesRunning = $true

                    # Check if core services are running
                    $coreServices = @("dynamodb", "s3", "lambda", "cognito-idp", "iam")
                    foreach ($service in $coreServices) {
                        if ($healthData.services.$service -ne "running") {
                            $allServicesRunning = $false
                            break
                        }
                    }

                    if ($allServicesRunning) {
                        Write-Status "All core LocalStack services are running"
                        break
                    }
                }
            }
            catch {
                # Continue waiting
            }

            Start-Sleep -Seconds 2
            $serviceCounter += 2

            if ($serviceCounter -ge $serviceTimeout) {
                Write-Warning "Some LocalStack services may not be fully ready, but continuing..."
                break
            }
        } while ($true)

        return $true
    }
    catch {
        Write-Host "[ERROR] Failed to start services: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Build and deploy Lambda functions with hot reload
function Build-LambdaFunctions {
    if ($SkipBuild) {
        Write-Status "Skipping Lambda function build (--SkipBuild specified)"
        return $true
    }

    Write-Status "Building and deploying Lambda functions with hot reload..."

    try {
        # Check if LocalStack is running
        $localstackRunning = $false
        try {
            Invoke-RestMethod -Uri "http://localhost:45660/_localstack/health" -TimeoutSec 5 -ErrorAction SilentlyContinue | Out-Null
            $localstackRunning = $true
        }
        catch {
            Write-Error "LocalStack is not running or not accessible"
            return $false
        }

        if (-not $localstackRunning) {
            Write-Error "LocalStack is not running"
            return $false
        }

        # Run the hot reload deployment script
        Write-Status "Starting Lambda hot reload deployment..."

        if ($BuildOnly) {
            & ".\deploy-lambda-hot-reload.ps1" -Environment "development" -BuildOnly
        }
        else {
            & ".\deploy-lambda-hot-reload.ps1" -Environment "development"
        }

        if ($LASTEXITCODE -eq 0) {
            Write-Status "Lambda functions deployed successfully with hot reload"

            # Show information about hot reload
            if (-not $BuildOnly) {
                Write-Host ""
                Write-Host "🔥 Hot reload is now active!" -ForegroundColor Yellow
                Write-Host "📝 Edit your TypeScript files in lambda-functions/*/src/ and they will be automatically recompiled and reloaded." -ForegroundColor Yellow
                Write-Host ""
            }

            return $true
        }
        else {
            Write-Error "Lambda function deployment failed"
            return $false
        }
    }
    catch {
        Write-Error "Failed to deploy Lambda functions: $_"
        return $false
    }
}

# Initialize AWS infrastructure using PowerShell scripts
function Initialize-AwsInfrastructure {
    Write-Status "Initializing AWS infrastructure..."

    try {
        # Check if LocalStack is running
        $localstackRunning = $false
        try {
            Invoke-RestMethod -Uri "http://localhost:45660/_localstack/health" -TimeoutSec 5 -ErrorAction SilentlyContinue | Out-Null
            $localstackRunning = $true
        }
        catch {
            Write-Error "LocalStack is not running or not accessible"
            return $false
        }

        if (-not $localstackRunning) {
            Write-Error "LocalStack is not running"
            return $false
        }

        # Run the AWS infrastructure initialization script
        Write-Status "Starting AWS infrastructure initialization..."

        # Deploy CloudFormation infrastructure first
        Write-Status "Deploying CloudFormation infrastructure..."

        # Set AWS credentials for LocalStack
        $env:AWS_ACCESS_KEY_ID = "test"
        $env:AWS_SECRET_ACCESS_KEY = "test"
        $env:AWS_DEFAULT_REGION = "us-east-1"

        # Check if stack already exists
        $stackExists = $false
        try {
            awslocal cloudformation describe-stacks --stack-name gameflex-infrastructure-development 2>$null | Out-Null
            if ($LASTEXITCODE -eq 0) {
                $stackExists = $true
                Write-Status "CloudFormation stack already exists, checking status..."
                $stackStatus = awslocal cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].StackStatus" --output text
                Write-Status "Current stack status: $stackStatus"

                if ($stackStatus -eq "CREATE_COMPLETE" -or $stackStatus -eq "UPDATE_COMPLETE") {
                    Write-Status "CloudFormation stack is already deployed and healthy"
                }
                else {
                    Write-Warning "CloudFormation stack exists but is in state: $stackStatus"
                    Write-Status "Attempting to update stack..."
                    awslocal cloudformation update-stack --stack-name gameflex-infrastructure-development --template-body file://cloudformation/gameflex-infrastructure.yaml --capabilities CAPABILITY_NAMED_IAM --parameters ParameterKey=Environment, ParameterValue=development ParameterKey=ProjectName, ParameterValue=gameflex 2>$null
                    if ($LASTEXITCODE -eq 0) {
                        Write-Status "Stack update initiated"
                    }
                    else {
                        Write-Status "No stack update needed or update failed, continuing..."
                    }
                }
            }
        }
        catch {
            Write-Status "CloudFormation stack does not exist, creating..."
        }

        if (-not $stackExists) {
            Write-Status "Creating CloudFormation stack..."
            awslocal cloudformation create-stack --stack-name gameflex-infrastructure-development --template-body file://cloudformation/gameflex-infrastructure.yaml --capabilities CAPABILITY_NAMED_IAM --parameters ParameterKey=Environment, ParameterValue=development ParameterKey=ProjectName, ParameterValue=gameflex

            if ($LASTEXITCODE -eq 0) {
                Write-Status "CloudFormation stack creation initiated"

                # Wait for stack to complete
                Write-Status "Waiting for CloudFormation stack to complete..."
                $timeout = 180
                $counter = 0

                do {
                    Start-Sleep -Seconds 5
                    $counter += 5

                    $stackStatus = awslocal cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].StackStatus" --output text 2>$null

                    if ($stackStatus -eq "CREATE_COMPLETE") {
                        Write-Status "CloudFormation stack created successfully"
                        break
                    }
                    elseif ($stackStatus -match "FAILED" -or $stackStatus -match "ROLLBACK") {
                        Write-Error "CloudFormation stack creation failed with status: $stackStatus"
                        awslocal cloudformation describe-stack-events --stack-name gameflex-infrastructure-development --query "StackEvents[?ResourceStatus=='CREATE_FAILED'].{Resource:LogicalResourceId,Reason:ResourceStatusReason}" --output table
                        return $false
                    }

                    if ($counter -ge $timeout) {
                        Write-Warning "CloudFormation stack creation timeout, but continuing..."
                        break
                    }

                    if ($counter % 15 -eq 0) {
                        Write-Status "Still waiting for CloudFormation stack... ($counter/$timeout seconds)"
                    }
                } while ($true)
            }
            else {
                Write-Error "Failed to create CloudFormation stack"
                return $false
            }
        }

        # Initialize AWS services (Cognito, DynamoDB, S3)
        Write-Status "Initializing AWS services..."
        & ".\init\init-aws-services.ps1"

        if ($LASTEXITCODE -eq 0) {
            Write-Status "AWS infrastructure initialized successfully"
            return $true
        }
        else {
            Write-Warning "AWS infrastructure initialization completed with warnings"
            return $true  # Continue even with warnings
        }
    }
    catch {
        Write-Error "Failed to initialize AWS infrastructure: $_"
        return $false
    }
}

# Legacy function - now handled by Docker containers
function Initialize-AwsServices {
    Write-Status "AWS services initialization is now handled by Docker containers"
    Write-Status "Use the new Docker-based approach instead"
}

# Handle Force mode - restart LocalStack completely
function Reset-LocalStack {
    if ($Force) {
        Write-Status "Force mode enabled - restarting LocalStack completely..."
        try {
            docker compose down
            Start-Sleep -Seconds 5

            # Clean up any orphaned containers
            docker system prune -f

            Write-Status "LocalStack reset completed"
            return $true
        }
        catch {
            Write-Error "Failed to reset LocalStack: $_"
            return $false
        }
    }
    return $true
}

# Legacy cleanup code removed - now handled by Docker containers





# Legacy function - now handled by Docker containers
function Deploy-Infrastructure {
    Write-Status "Infrastructure deployment is now handled by Docker containers"
    Write-Status "Use the new Docker-based approach instead"
    return $true
}

# Display service information
function Show-ServiceInfo {
    Write-Header "GameFlex AWS Backend (Docker-based) is now running!"
    Write-Host ""

    # Get API Gateway URL from CloudFormation stack outputs
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    $env:AWS_DEFAULT_REGION = "us-east-1"
    $ENDPOINT_URL = "http://localhost:45660"

    try {
        $apiUrl = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].Outputs[?OutputKey=='ApiGatewayUrl'].OutputValue" --output text 2>$null
        if ($apiUrl) {
            Write-Host "  API Gateway URL: $apiUrl" -ForegroundColor Cyan
        }
    }
    catch {
        # Continue if we can't get the API URL
    }

    Write-Status "Service URLs:"
    Write-Host "  LocalStack Health: http://localhost:45660/_localstack/health" -ForegroundColor Cyan
    Write-Host "  LocalStack Pro Dashboard: http://localhost:4566/_localstack/health" -ForegroundColor Cyan
    Write-Host "  DynamoDB (via LocalStack): http://localhost:4566" -ForegroundColor Cyan
    Write-Host "  S3 Console: http://localhost:45660/_aws/s3" -ForegroundColor Cyan
    Write-Host "  Cognito Console: http://localhost:45660/_aws/cognito" -ForegroundColor Cyan
    Write-Host ""

    Write-Status "Development Credentials:"
    Write-Host "  Developer: <EMAIL> / DevPassword123!" -ForegroundColor Cyan
    Write-Host "  Admin: <EMAIL> / AdminPassword123!" -ForegroundColor Cyan
    Write-Host ""

    Write-Status "Useful Commands:"
    Write-Host "  Check status: docker compose ps" -ForegroundColor Cyan
    Write-Host "  View logs: docker compose logs -f localstack" -ForegroundColor Cyan
    Write-Host "  Build only: .\start.ps1 -BuildOnly" -ForegroundColor Cyan
    Write-Host "  Skip build: .\start.ps1 -SkipBuild" -ForegroundColor Cyan
    Write-Host "  Hot reload deploy: .\deploy-lambda-hot-reload.ps1" -ForegroundColor Cyan
    Write-Host "  Stop services: .\stop.ps1" -ForegroundColor Cyan
    Write-Host "  Restart: .\stop.ps1; .\start.ps1" -ForegroundColor Cyan
    Write-Host ""

    Write-Status "Docker Services:"
    docker compose ps
    Write-Host ""

    Write-Warning "This is a development environment. Do not use in production!"
}

# Main execution
function Main {
    Write-Header "GameFlex AWS Backend Startup (Hot Reload)"
    Write-Host ""

    # Load environment variables from .env file
    Load-EnvFile

    if (-not (Test-Docker)) {
        exit 1
    }

    if (-not (Test-Ports)) {
        exit 1
    }

    New-Directories

    # Handle Force mode
    if (-not (Reset-LocalStack)) {
        exit 1
    }

    # Start LocalStack service and wait for it to be ready
    Write-Status "Step 1: Starting LocalStack service..."
    if (-not (Start-Services)) {
        Write-Error "Failed to start LocalStack service"
        exit 1
    }

    # If BuildOnly is specified, just build and exit
    if ($BuildOnly) {
        Write-Status "Build-only mode specified"
        Write-Status "Step 2: Building Lambda functions..."
        if (-not (Build-LambdaFunctions)) {
            Write-Error "Lambda function build failed"
            exit 1
        }
        Write-Status "Build completed successfully!"
        return
    }

    # Build and deploy Lambda functions with hot reload
    Write-Status "Step 2: Building and deploying Lambda functions with hot reload..."
    if (-not (Build-LambdaFunctions)) {
        Write-Error "Lambda function deployment failed."
        Write-Status "You can check the build logs in the logs/ directory"
        Write-Status "Or try running the deployment script directly:"
        Write-Host "  .\deploy-lambda-hot-reload.ps1" -ForegroundColor Cyan
        exit 1
    }

    # Initialize AWS infrastructure
    Write-Status "Step 3: Initializing AWS infrastructure..."
    if (-not (Initialize-AwsInfrastructure)) {
        Write-Error "AWS infrastructure initialization failed."
        Write-Status "You can try running the initialization scripts directly:"
        Write-Host "  .\deploy-infrastructure.ps1" -ForegroundColor Cyan
        Write-Host "  .\init\init-aws-services.ps1" -ForegroundColor Cyan
        Write-Status "Or try running with -Force to restart LocalStack completely:"
        Write-Host "  .\stop.ps1; .\start.ps1 -Force" -ForegroundColor Cyan
        exit 1
    }

    Show-ServiceInfo

    Write-Status "Startup completed successfully!"
}

# Run main function
try {
    Main
}
catch {
    Write-Host "[ERROR] Startup failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
