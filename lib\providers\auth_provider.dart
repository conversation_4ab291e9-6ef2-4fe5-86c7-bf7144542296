import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/aws_auth_service.dart' as aws;

enum AuthStatus { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  AuthStatus _status = AuthStatus.initial;
  UserModel? _user;
  String? _errorMessage;
  StreamSubscription<aws.AuthState>? _authSubscription;

  AuthStatus get status => _status;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated =>
      _status == AuthStatus.authenticated && _user != null;
  bool get isLoading => _status == AuthStatus.loading;

  AuthProvider() {
    _initializeAuth();
  }

  /// Initialize authentication state
  Future<void> _initializeAuth() async {
    _setStatus(AuthStatus.loading);

    try {
      // Try to restore session from stored tokens
      final sessionRestored =
          await aws.AwsAuthService.instance.restoreSession();

      if (sessionRestored && aws.AwsAuthService.instance.isAuthenticated) {
        final awsUser = aws.AwsAuthService.instance.currentUser;
        if (awsUser != null) {
          _user = UserModel.fromAwsUser(awsUser);
          _setStatus(AuthStatus.authenticated);
        } else {
          _setStatus(AuthStatus.unauthenticated);
        }
      } else {
        _setStatus(AuthStatus.unauthenticated);
      }

      // Listen to auth state changes
      _authSubscription = aws.AwsAuthService.instance.authStateChanges.listen(
        _onAuthStateChange,
        onError: (error) {
          _setError('Authentication error: $error');
        },
      );
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
    }
  }

  /// Handle auth state changes
  void _onAuthStateChange(aws.AuthState authState) {
    switch (authState) {
      case aws.AuthState.signedIn:
        final awsUser = aws.AwsAuthService.instance.currentUser;
        if (awsUser != null) {
          _user = UserModel.fromAwsUser(awsUser);
          _setStatus(AuthStatus.authenticated);
        }
        break;
      case aws.AuthState.signedOut:
        _user = null;
        _setStatus(AuthStatus.unauthenticated);
        break;
    }
  }

  /// Sign up with email and password
  Future<bool> signUp({required String email, required String password}) async {
    developer.log('AuthProvider: Starting sign up process for email: $email');
    if (kDebugMode) {
      print('AuthProvider: Starting sign up process for email: $email');
    }

    _setStatus(AuthStatus.loading);

    try {
      // Generate username from email
      final username = email.split('@').first.toLowerCase();

      final response = await aws.AwsAuthService.instance.signUp(
        email: email,
        password: password,
        username: username,
      );

      developer.log(
        'AuthProvider: Sign up response received - Success: ${response.success}',
      );
      if (kDebugMode) {
        print('AuthProvider: Sign up response - Success: ${response.success}');
        print('AuthProvider: Message: ${response.message}');
      }

      if (response.success && response.user != null) {
        developer.log('AuthProvider: Sign up completed successfully');
        if (kDebugMode) {
          print('AuthProvider: Sign up completed successfully');
        }

        // Note: For AWS, user will need to sign in after signup
        _setStatus(AuthStatus.unauthenticated);
        return true;
      } else {
        final errorMsg = response.message;
        developer.log('AuthProvider: $errorMsg');
        if (kDebugMode) {
          print('AuthProvider: $errorMsg');
        }
        _setErrorWithoutStatusChange(errorMsg);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Sign up failed: $e';
      developer.log(
        'AuthProvider: Error during sign up',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('AuthProvider: SIGN UP ERROR: $e');
        print('AuthProvider: SIGN UP STACK TRACE: $stackTrace');
      }
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign in with email and password
  Future<bool> signIn({required String email, required String password}) async {
    developer.log('AuthProvider: Starting sign in process for email: $email');
    if (kDebugMode) {
      print('AuthProvider: Starting sign in process for email: $email');
    }

    _setStatus(AuthStatus.loading);

    try {
      final response = await aws.AwsAuthService.instance.signIn(
        email: email,
        password: password,
      );

      developer.log(
        'AuthProvider: Sign in response received - Success: ${response.success}',
      );
      if (kDebugMode) {
        print('AuthProvider: Sign in response - Success: ${response.success}');
        print('AuthProvider: Message: ${response.message}');
      }

      if (response.success && response.user != null) {
        _user = UserModel.fromAwsUser(response.user!);
        _setStatus(AuthStatus.authenticated);

        developer.log('AuthProvider: Sign in completed successfully');
        if (kDebugMode) {
          print('AuthProvider: Sign in completed successfully');
        }
        return true;
      } else {
        final errorMsg = response.message;
        developer.log('AuthProvider: $errorMsg');
        if (kDebugMode) {
          print('AuthProvider: $errorMsg');
        }
        _setErrorWithoutStatusChange(errorMsg);
        return false;
      }
    } catch (e, stackTrace) {
      final errorMsg = 'Sign in failed: $e';
      developer.log(
        'AuthProvider: Error during sign in',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('AuthProvider: SIGN IN ERROR: $e');
        print('AuthProvider: SIGN IN STACK TRACE: $stackTrace');
      }
      _setErrorWithoutStatusChange(errorMsg);
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    _setStatus(AuthStatus.loading);

    try {
      await aws.AwsAuthService.instance.signOut();
      _user = null;
      _setStatus(AuthStatus.unauthenticated);
    } catch (e) {
      _setError('Sign out failed: $e');
    }
  }

  /// Reset password (not implemented in AWS backend yet)
  Future<bool> resetPassword(String email) async {
    _setError('Password reset is not implemented yet');
    return false;
  }

  /// Change password for the current user (not implemented in AWS backend yet)
  Future<bool> changePassword(String newPassword) async {
    _setError('Password change is not implemented yet');
    return false;
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Set status and notify listeners
  void _setStatus(AuthStatus status) {
    _status = status;
    if (status != AuthStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error and notify listeners
  void _setError(String error) {
    _errorMessage = error;
    _status = AuthStatus.error;
    notifyListeners();
  }

  /// Set error without changing status (for login/signup failures)
  void _setErrorWithoutStatusChange(String error) {
    _errorMessage = error;
    // Reset loading status back to unauthenticated if we're currently loading
    if (_status == AuthStatus.loading) {
      _status = AuthStatus.unauthenticated;
    }
    notifyListeners();
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }
}
