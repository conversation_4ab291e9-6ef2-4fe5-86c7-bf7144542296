import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/config_service.dart';
import 'test_helpers.dart';

void main() {
  group('Configuration Service Tests', () {
    late ConfigService configService;

    setUpAll(() async {
      TestHelpers.setupTestEnvironment();
    });

    setUp(() async {
      configService = ConfigService.instance;
      configService.clearCache();
    });

    test('should initialize with default values', () async {
      final serverUrl = await configService.getServerUrl();
      final options = configService.getServerUrlOptions();

      expect(serverUrl, isNotNull);
      expect(options, isNotEmpty);

      print('✅ ConfigService initialized');
      print('   Current server URL: $serverUrl');
      print('   Available options: ${options.map((o) => o.name).toList()}');
    });

    test('should have localhost option for development', () {
      final options = configService.getServerUrlOptions();

      expect(options.any((option) => option.url.contains('localhost')), isTrue);

      print('✅ Localhost option available for development');
    });

    test('should have gameflex.io option for production', () {
      final options = configService.getServerUrlOptions();

      expect(
        options.any((option) => option.url.contains('gameflex.io')),
        isTrue,
      );

      print('✅ GameFlex.io option available for production');
    });

    test('should change server URL in development mode', () async {
      if (!configService.isDevelopment) {
        print('ℹ️  Skipping URL change test - not in development mode');
        return;
      }

      final originalUrl = await configService.getServerUrl();
      final options = configService.getServerUrlOptions();

      // Find a different URL to switch to
      final newOption = options.firstWhere(
        (option) => option.url != originalUrl && option.url != 'auto',
        orElse: () => options.first,
      );

      await configService.setServerUrl(newOption.url, newOption.name);

      final updatedUrl = await configService.getServerUrl();
      expect(updatedUrl, equals(newOption.url));

      print('✅ Server URL changed successfully');
      print('   Original: $originalUrl');
      print('   New: $updatedUrl');

      // Change back to auto-detect
      await configService.enableAutoDetection();
    });

    test('should provide environment information', () {
      final environmentName = configService.getEnvironmentName();
      final environmentColor = configService.getEnvironmentColor();

      expect(environmentName, isNotNull);
      expect(environmentColor, isNotNull);
      expect(
        ['Development', 'Staging', 'Production'].contains(environmentName),
        isTrue,
      );

      print('✅ Environment information available');
      print('   Environment: $environmentName');
      print('   Color: $environmentColor');
      print('   Is Development: ${configService.isDevelopment}');
      print('   Is Staging: ${configService.isStaging}');
      print('   Is Production: ${configService.isProduction}');
    });

    test('should provide debug information', () async {
      final debugInfo = await configService.getDebugInfo();

      expect(debugInfo, isA<Map<String, dynamic>>());
      expect(debugInfo['environment'], isNotNull);
      expect(debugInfo['currentServerUrl'], isNotNull);
      expect(debugInfo['platform'], isNotNull);

      print('✅ Debug information available');
      print('   Environment: ${debugInfo['environment']}');
      print('   Current URL: ${debugInfo['currentServerUrl']}');
      print('   Platform: ${debugInfo['platform']}');
      print('   Is Web: ${debugInfo['isWeb']}');
      print('   Is Emulator: ${debugInfo['isEmulator']}');
    });
  });
}
