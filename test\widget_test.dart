// GameFlex widget tests for AWS backend integration

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/main.dart';
import 'test_helpers.dart';

void main() {
  setUpAll(() {
    TestHelpers.setupTestEnvironment();
  });

  testWidgets('GameFlex app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());
    await tester.pumpAndSettle();

    // Verify that the app starts and shows some basic UI
    // The app should show the splash screen or login screen
    expect(find.byType(MaterialApp), findsOneWidget);

    // The app should have the correct title
    final MaterialApp app = tester.widget(find.byType(MaterialApp));
    expect(app.title, equals('GameFlex'));

    print('✅ GameFlex app widget test passed');
  });

  testWidgets('App theme configuration test', (WidgetTester tester) async {
    await tester.pumpWidget(const MyApp());
    await tester.pumpAndSettle();

    final MaterialApp app = tester.widget(find.byType(MaterialApp));

    // Verify theme configuration
    expect(app.theme, isNotNull);
    expect(app.darkTheme, isNotNull);
    expect(app.themeMode, equals(ThemeMode.dark));

    print('✅ App theme configuration test passed');
  });

  testWidgets('Provider setup test', (WidgetTester tester) async {
    await tester.pumpWidget(const MyApp());
    await tester.pumpAndSettle();

    // The app should have providers set up
    // This test verifies that the widget tree builds without provider errors
    expect(find.byType(MaterialApp), findsOneWidget);

    print('✅ Provider setup test passed');
  });
}
