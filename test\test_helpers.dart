import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

/// Test helpers for mocking plugins and setting up test environment
class TestHelpers {
  /// Mock SharedPreferences plugin to prevent MissingPluginException
  static void mockSharedPreferences() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
          const MethodChannel('plugins.flutter.io/shared_preferences'),
          (MethodCall methodCall) async {
            switch (methodCall.method) {
              case 'getAll':
                return <String, dynamic>{};
              case 'getBool':
              case 'getInt':
              case 'getDouble':
              case 'getString':
              case 'getStringList':
                return null;
              case 'setBool':
              case 'setInt':
              case 'setDouble':
              case 'setString':
              case 'setStringList':
              case 'remove':
              case 'clear':
                return true;
              default:
                return null;
            }
          },
        );
  }

  /// Setup test environment with all necessary mocks
  /// Set allowRealHttp to true for integration tests that need real network calls
  static void setupTestEnvironment({bool allowRealHttp = false}) {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockSharedPreferences();

    if (allowRealHttp) {
      // Allow real HTTP requests for integration tests
      print('🌐 Test environment: Allowing real HTTP requests');
    } else {
      // Mock HTTP requests for unit tests
      print('🧪 Test environment: Mocking HTTP requests');
    }
  }

  /// Setup integration test environment that allows real HTTP calls
  static void setupIntegrationTestEnvironment() {
    setupTestEnvironment(allowRealHttp: true);
  }

  /// Clear all mocks (useful for tearDown)
  static void clearMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
          const MethodChannel('plugins.flutter.io/shared_preferences'),
          null,
        );
  }
}
