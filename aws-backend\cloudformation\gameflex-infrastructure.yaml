AWSTemplateFormatVersion: "2010-09-09"
Description: "GameFlex AWS Infrastructure - Unified Template for All Environments"

Parameters:
  Environment:
    Type: String
    Default: development
    AllowedValues:
      - development
      - staging
      - production
    Description: Environment name

  ProjectName:
    Type: String
    Default: gameflex
    Description: Project name for resource naming

Conditions:
  IsDevelopment: !Equals [!Ref Environment, development]
  IsProduction: !Equals [!Ref Environment, production]

Resources:
  # S3 Buckets
  MediaBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      BucketName: !Sub "${ProjectName}-media-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - "*"
            MaxAge: 3000

  AvatarsBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      BucketName: !Sub "${ProjectName}-avatars-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - "*"
            MaxAge: 3000

  TempBucket:
    Type: AWS::S3::Bucket
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      BucketName: !Sub "${ProjectName}-temp-${Environment}"
      LifecycleConfiguration:
        Rules:
          - Id: DeleteTempFiles
            Status: Enabled
            ExpirationInDays: 1

  # DynamoDB Tables
  UsersTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Users"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: email
          AttributeType: S
        - AttributeName: username
          AttributeType: S
        - AttributeName: cognito_user_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: EmailIndex
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: UsernameIndex
          KeySchema:
            - AttributeName: username
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: CognitoUserIdIndex
          KeySchema:
            - AttributeName: cognito_user_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST

  PostsTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Posts"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  MediaTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Media"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  # Cognito User Pool
  UserPool:
    Type: AWS::Cognito::UserPool
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      UserPoolName: !Sub "${ProjectName}-users-${Environment}"
      AutoVerifiedAttributes:
        - email
      UsernameAttributes:
        - email
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: false

  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      ClientName: !Sub "${ProjectName}-client-${Environment}"
      UserPoolId: !Ref UserPool
      GenerateSecret: true
      ExplicitAuthFlows:
        - ADMIN_NO_SRP_AUTH
        - ALLOW_USER_PASSWORD_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH
      RefreshTokenValidity: 30
      AccessTokenValidity: 60
      IdTokenValidity: 60

  # Lambda Execution Role
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      RoleName: !Sub "${ProjectName}-lambda-execution-role-${Environment}"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: CognitoAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - cognito-idp:*
                Resource: !GetAtt UserPool.Arn
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:GetItem
                  - dynamodb:PutItem
                  - dynamodb:UpdateItem
                  - dynamodb:DeleteItem
                  - dynamodb:Query
                  - dynamodb:Scan
                Resource:
                  - !GetAtt UsersTable.Arn
                  - !Sub "${UsersTable.Arn}/index/*"
                  - !GetAtt PostsTable.Arn
                  - !GetAtt MediaTable.Arn
        - PolicyName: S3Access
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:ListBucket
                Resource:
                  - !GetAtt MediaBucket.Arn
                  - !Sub "${MediaBucket.Arn}/*"
                  - !GetAtt AvatarsBucket.Arn
                  - !Sub "${AvatarsBucket.Arn}/*"
                  - !GetAtt TempBucket.Arn
                  - !Sub "${TempBucket.Arn}/*"

  # API Gateway
  ApiGateway:
    Type: AWS::ApiGateway::RestApi
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      Name: !Sub "${ProjectName}-api-${Environment}"
      Description: GameFlex API Gateway
      EndpointConfiguration:
        Types:
          - REGIONAL

  # API Gateway Deployment
  ApiDeployment:
    Type: AWS::ApiGateway::Deployment
    Properties:
      RestApiId: !Ref ApiGateway
      StageName: !Ref Environment

Outputs:
  UserPoolId:
    Description: Cognito User Pool ID
    Value: !Ref UserPool
    Export:
      Name: !Sub "${ProjectName}-user-pool-id-${Environment}"

  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value: !Ref UserPoolClient
    Export:
      Name: !Sub "${ProjectName}-user-pool-client-id-${Environment}"

  ApiGatewayId:
    Description: API Gateway ID
    Value: !Ref ApiGateway
    Export:
      Name: !Sub "${ProjectName}-api-gateway-id-${Environment}"

  ApiGatewayUrl:
    Description: API Gateway URL
    Value: !If
      - IsDevelopment
      - !Sub "http://localhost:45660/restapis/${ApiGateway}/development/_user_request_"
      - !Sub "https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Environment}"
    Export:
      Name: !Sub "${ProjectName}-api-url-${Environment}"

  MediaBucketName:
    Description: S3 Media Bucket Name
    Value: !Ref MediaBucket
    Export:
      Name: !Sub "${ProjectName}-media-bucket-${Environment}"

  AvatarsBucketName:
    Description: S3 Avatars Bucket Name
    Value: !Ref AvatarsBucket
    Export:
      Name: !Sub "${ProjectName}-avatars-bucket-${Environment}"

  TempBucketName:
    Description: S3 Temp Bucket Name
    Value: !Ref TempBucket
    Export:
      Name: !Sub "${ProjectName}-temp-bucket-${Environment}"

  UsersTableName:
    Description: DynamoDB Users Table Name
    Value: !Ref UsersTable
    Export:
      Name: !Sub "${ProjectName}-users-table-${Environment}"

  PostsTableName:
    Description: DynamoDB Posts Table Name
    Value: !Ref PostsTable
    Export:
      Name: !Sub "${ProjectName}-posts-table-${Environment}"

  MediaTableName:
    Description: DynamoDB Media Table Name
    Value: !Ref MediaTable
    Export:
      Name: !Sub "${ProjectName}-media-table-${Environment}"

  LambdaExecutionRoleArn:
    Description: Lambda Execution Role ARN
    Value: !GetAtt LambdaExecutionRole.Arn
    Export:
      Name: !Sub "${ProjectName}-lambda-execution-role-arn-${Environment}"
