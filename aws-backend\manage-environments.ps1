# GameFlex Environment Management Script (PowerShell)
# This script helps manage different environments and their configurations

param(
    [string]$Action = "list",
    [string]$Environment = "",
    [string]$ProjectName = "gameflex",
    [string]$Region = "us-east-1",
    [string]$AwsProfile = "default",
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Available environments
$ValidEnvironments = @("development", "qa", "staging", "production")

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[ENV-MGMT] $Message" -ForegroundColor Blue
}

# List all environments and their status
function Get-EnvironmentStatus {
    Write-Header "GameFlex Environment Status"
    Write-Host ""
    
    foreach ($env in $ValidEnvironments) {
        $stackName = "$ProjectName-infrastructure-$env"
        $isLocalStack = $env -eq "development"
        
        Write-Host "Environment: $env" -ForegroundColor Cyan
        Write-Host "  Stack Name: $stackName"
        
        # Check if configuration files exist
        $paramFile = "cloudformation\parameters\$env.json"
        $envFile = ".env.$env"
        
        if (Test-Path $paramFile) {
            Write-Host "  Parameters File: ✓ $paramFile" -ForegroundColor Green
        } else {
            Write-Host "  Parameters File: ✗ $paramFile (missing)" -ForegroundColor Red
        }
        
        if (Test-Path $envFile) {
            Write-Host "  Environment File: ✓ $envFile" -ForegroundColor Green
        } else {
            Write-Host "  Environment File: ✗ $envFile (missing)" -ForegroundColor Red
        }
        
        # Check stack status
        try {
            if ($isLocalStack) {
                $stackStatus = aws --endpoint-url=http://localhost:4566 cloudformation describe-stacks --stack-name $stackName --query "Stacks[0].StackStatus" --output text 2>$null
            } else {
                $stackStatus = aws cloudformation describe-stacks --stack-name $stackName --profile $AwsProfile --region $Region --query "Stacks[0].StackStatus" --output text 2>$null
            }
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  Stack Status: $stackStatus" -ForegroundColor Green
            } else {
                Write-Host "  Stack Status: Not deployed" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "  Stack Status: Unknown (check AWS connection)" -ForegroundColor Red
        }
        
        Write-Host ""
    }
}

# Validate environment configuration
function Test-EnvironmentConfig {
    param([string]$EnvName)
    
    Write-Header "Validating Environment Configuration: $EnvName"
    Write-Host ""
    
    $isValid = $true
    
    # Check if environment is valid
    if ($EnvName -notin $ValidEnvironments) {
        Write-Error "Invalid environment: $EnvName. Valid environments: $($ValidEnvironments -join ', ')"
        return $false
    }
    
    # Check parameters file
    $paramFile = "cloudformation\parameters\$EnvName.json"
    if (-not (Test-Path $paramFile)) {
        Write-Error "Parameters file missing: $paramFile"
        $isValid = $false
    } else {
        Write-Status "Parameters file found: $paramFile"
        
        # Validate JSON
        try {
            $params = Get-Content $paramFile | ConvertFrom-Json
            Write-Status "Parameters file is valid JSON"
            
            # Check for placeholder values in non-development environments
            if ($EnvName -ne "development") {
                foreach ($param in $params) {
                    if ($param.ParameterValue -like "*ACCOUNT_ID*" -or $param.ParameterValue -like "*CERTIFICATE_ID*") {
                        Write-Warning "Parameter '$($param.ParameterKey)' contains placeholder value: $($param.ParameterValue)"
                        $isValid = $false
                    }
                }
            }
        }
        catch {
            Write-Error "Parameters file is not valid JSON: $_"
            $isValid = $false
        }
    }
    
    # Check environment file
    $envFile = ".env.$EnvName"
    if (-not (Test-Path $envFile)) {
        Write-Warning "Environment file missing: $envFile (optional)"
    } else {
        Write-Status "Environment file found: $envFile"
    }
    
    # Check CloudFormation template
    $templateFile = if ($EnvName -eq "development") {
        "cloudformation\gameflex-simple-infrastructure.yaml"
    } else {
        "cloudformation\gameflex-production-infrastructure.yaml"
    }
    
    if (-not (Test-Path $templateFile)) {
        Write-Error "CloudFormation template missing: $templateFile"
        $isValid = $false
    } else {
        Write-Status "CloudFormation template found: $templateFile"
    }
    
    # Check AWS connectivity
    $isLocalStack = $EnvName -eq "development"
    try {
        if ($isLocalStack) {
            $result = aws --endpoint-url=http://localhost:4566 sts get-caller-identity 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Status "LocalStack connectivity: OK"
            } else {
                Write-Warning "LocalStack not accessible (make sure it's running)"
            }
        } else {
            $result = aws sts get-caller-identity --profile $AwsProfile --region $Region 2>$null
            if ($LASTEXITCODE -eq 0) {
                $identity = $result | ConvertFrom-Json
                Write-Status "AWS connectivity: OK (Account: $($identity.Account))"
            } else {
                Write-Warning "AWS not accessible (check credentials and profile)"
            }
        }
    }
    catch {
        Write-Warning "Failed to check AWS connectivity: $_"
    }
    
    Write-Host ""
    if ($isValid) {
        Write-Status "Environment configuration is valid ✓"
    } else {
        Write-Error "Environment configuration has issues ✗"
    }
    
    return $isValid
}

# Deploy specific environment
function Deploy-Environment {
    param([string]$EnvName)
    
    Write-Header "Deploying Environment: $EnvName"
    Write-Host ""
    
    # Validate configuration first
    if (-not (Test-EnvironmentConfig -EnvName $EnvName)) {
        Write-Error "Environment configuration validation failed"
        return $false
    }
    
    # Choose appropriate deployment script
    $deployScript = switch ($EnvName) {
        "development" { ".\deploy-infrastructure.ps1" }
        "qa" { ".\deploy-qa.ps1" }
        "staging" { ".\deploy-staging.ps1" }
        "production" { ".\deploy-production.ps1" }
    }
    
    if (-not (Test-Path $deployScript)) {
        Write-Error "Deployment script not found: $deployScript"
        return $false
    }
    
    Write-Status "Using deployment script: $deployScript"
    
    # Execute deployment
    try {
        & $deployScript -Environment $EnvName -ProjectName $ProjectName -Region $Region -AwsProfile $AwsProfile
        return $true
    }
    catch {
        Write-Error "Deployment failed: $_"
        return $false
    }
}

# Destroy environment
function Remove-Environment {
    param([string]$EnvName)
    
    Write-Header "Destroying Environment: $EnvName"
    Write-Host ""
    
    $stackName = "$ProjectName-infrastructure-$EnvName"
    $isLocalStack = $EnvName -eq "development"
    
    # Safety check for production
    if ($EnvName -eq "production") {
        Write-Warning "🚨 DANGER: You are about to destroy the PRODUCTION environment! 🚨"
        Write-Warning "This action cannot be undone and will delete all production data!"
        $confirmation1 = Read-Host "Type 'DELETE PRODUCTION' to confirm"
        if ($confirmation1 -ne "DELETE PRODUCTION") {
            Write-Status "Destruction cancelled"
            return $false
        }
        
        $confirmation2 = Read-Host "Are you absolutely sure? (yes/no)"
        if ($confirmation2 -ne "yes") {
            Write-Status "Destruction cancelled"
            return $false
        }
    } else {
        $confirmation = Read-Host "Are you sure you want to destroy the $EnvName environment? (yes/no)"
        if ($confirmation -ne "yes") {
            Write-Status "Destruction cancelled"
            return $false
        }
    }
    
    try {
        Write-Status "Deleting CloudFormation stack: $stackName"
        
        if ($isLocalStack) {
            aws --endpoint-url=http://localhost:4566 cloudformation delete-stack --stack-name $stackName
        } else {
            # Disable termination protection first for production
            if ($EnvName -eq "production") {
                Write-Status "Disabling termination protection..."
                aws cloudformation update-termination-protection --no-enable-termination-protection --stack-name $stackName --profile $AwsProfile --region $Region
            }
            
            aws cloudformation delete-stack --stack-name $stackName --profile $AwsProfile --region $Region
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Stack deletion initiated successfully"
            Write-Status "Monitor the deletion progress in the AWS Console"
            return $true
        } else {
            Write-Error "Failed to initiate stack deletion"
            return $false
        }
    }
    catch {
        Write-Error "Failed to destroy environment: $_"
        return $false
    }
}

# Main function
function Invoke-EnvironmentManagement {
    switch ($Action.ToLower()) {
        "list" {
            Get-EnvironmentStatus
        }
        "validate" {
            if (-not $Environment) {
                Write-Error "Environment parameter required for validate action"
                exit 1
            }
            Test-EnvironmentConfig -EnvName $Environment
        }
        "deploy" {
            if (-not $Environment) {
                Write-Error "Environment parameter required for deploy action"
                exit 1
            }
            Deploy-Environment -EnvName $Environment
        }
        "destroy" {
            if (-not $Environment) {
                Write-Error "Environment parameter required for destroy action"
                exit 1
            }
            Remove-Environment -EnvName $Environment
        }
        default {
            Write-Error "Invalid action: $Action. Valid actions: list, validate, deploy, destroy"
            exit 1
        }
    }
}

# Show help if requested
if ($args -contains "-h" -or $args -contains "--help") {
    Write-Host "GameFlex Environment Management Script" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Usage: .\manage-environments.ps1 [OPTIONS]" -ForegroundColor Green
    Write-Host ""
    Write-Host "Actions:" -ForegroundColor Yellow
    Write-Host "  list        List all environments and their status (default)"
    Write-Host "  validate    Validate environment configuration"
    Write-Host "  deploy      Deploy environment infrastructure"
    Write-Host "  destroy     Destroy environment infrastructure"
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Action         Action to perform (list, validate, deploy, destroy)"
    Write-Host "  -Environment    Environment name (development, qa, staging, production)"
    Write-Host "  -ProjectName    Project name (default: gameflex)"
    Write-Host "  -Region         AWS region (default: us-east-1)"
    Write-Host "  -AwsProfile     AWS profile (default: default)"
    Write-Host "  -Verbose        Show verbose output"
    Write-Host "  -h, --help      Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\manage-environments.ps1"
    Write-Host "  .\manage-environments.ps1 -Action validate -Environment qa"
    Write-Host "  .\manage-environments.ps1 -Action deploy -Environment staging"
    Write-Host "  .\manage-environments.ps1 -Action destroy -Environment development"
    exit 0
}

# Run the management function
try {
    Invoke-EnvironmentManagement
}
catch {
    Write-Error "Environment management failed: $_"
    exit 1
}
