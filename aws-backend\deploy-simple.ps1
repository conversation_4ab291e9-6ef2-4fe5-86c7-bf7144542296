# GameFlex Simple Deployment Script (PowerShell)
# Deploys only the services that work well with free LocalStack

param(
    [string]$Environment = "development",
    [string]$ProjectName = "gameflex",
    [switch]$Force,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# AWS CLI configuration for LocalStack
$env:AWS_ACCESS_KEY_ID = "test"
$env:AWS_SECRET_ACCESS_KEY = "test"
$env:AWS_DEFAULT_REGION = "us-east-1"
$ENDPOINT_URL = "http://localhost:45660"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[DEPLOY] $Message" -ForegroundColor Blue
}

# Test AWS CLI and LocalStack connectivity
function Test-AwsConnection {
    try {
        $result = aws --endpoint-url=$ENDPOINT_URL sts get-caller-identity 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "AWS CLI connection to LocalStack successful"
            return $true
        }
        else {
            Write-Error "Failed to connect to LocalStack"
            return $false
        }
    }
    catch {
        Write-Error "AWS CLI not available or LocalStack not accessible: $_"
        return $false
    }
}

# Create S3 buckets
function New-S3Buckets {
    Write-Status "Creating S3 buckets..."
    
    $buckets = @(
        "gameflex-media-development",
        "gameflex-avatars-development", 
        "gameflex-temp-development"
    )
    
    foreach ($bucket in $buckets) {
        try {
            # Check if bucket exists
            $exists = aws --endpoint-url=$ENDPOINT_URL s3api head-bucket --bucket $bucket 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Status "Bucket $bucket already exists"
            }
            else {
                # Create bucket
                aws --endpoint-url=$ENDPOINT_URL s3api create-bucket --bucket $bucket | Out-Null
                Write-Status "Created S3 bucket: $bucket"
                
                # Set bucket policy for public read access (development only)
                $policy = @{
                    Version   = "2012-10-17"
                    Statement = @(
                        @{
                            Sid       = "PublicReadGetObject"
                            Effect    = "Allow"
                            Principal = "*"
                            Action    = "s3:GetObject"
                            Resource  = "arn:aws:s3:::$bucket/*"
                        }
                    )
                } | ConvertTo-Json -Depth 10
                
                $policy | Out-File -FilePath "temp-policy.json" -Encoding UTF8
                aws --endpoint-url=$ENDPOINT_URL s3api put-bucket-policy --bucket $bucket --policy file://temp-policy.json | Out-Null
                Remove-Item "temp-policy.json" -Force
                
                Write-Status "Set public read policy for bucket: $bucket"
            }
        }
        catch {
            Write-Error "Failed to create bucket $bucket : $_"
        }
    }
}

# Create simple Lambda functions
function New-LambdaFunctions {
    Write-Status "Creating Lambda functions..."
    
    # Create IAM role first
    $roleName = "gameflex-lambda-role"
    $rolePolicy = @{
        Version   = "2012-10-17"
        Statement = @(
            @{
                Effect    = "Allow"
                Principal = @{
                    Service = "lambda.amazonaws.com"
                }
                Action    = "sts:AssumeRole"
            }
        )
    } | ConvertTo-Json -Depth 10
    
    try {
        $rolePolicy | Out-File -FilePath "temp-role-policy.json" -Encoding UTF8
        $roleResult = aws --endpoint-url=$ENDPOINT_URL iam create-role --role-name $roleName --assume-role-policy-document file://temp-role-policy.json 2>$null
        Remove-Item "temp-role-policy.json" -Force
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Created IAM role: $roleName"
        }
        else {
            Write-Status "IAM role already exists: $roleName"
        }
    }
    catch {
        Write-Warning "Failed to create IAM role: $_"
    }
    
    # Create Lambda functions
    $functions = @(
        @{
            Name    = "gameflex-auth-development"
            Handler = "index.handler"
            Code    = @"
exports.handler = async (event, context) => {
    console.log('Auth function called with event:', JSON.stringify(event));
    
    const path = event.path || event.rawPath || '';
    const method = event.httpMethod || event.requestContext?.http?.method || 'GET';
    
    const response = {
        statusCode: 200,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        },
        body: JSON.stringify({
            message: 'GameFlex Auth Service',
            path: path,
            method: method,
            timestamp: new Date().toISOString(),
            environment: 'development'
        })
    };
    
    return response;
};
"@
        },
        @{
            Name    = "gameflex-posts-development"
            Handler = "index.handler"
            Code    = @"
exports.handler = async (event, context) => {
    console.log('Posts function called with event:', JSON.stringify(event));
    
    const path = event.path || event.rawPath || '';
    const method = event.httpMethod || event.requestContext?.http?.method || 'GET';
    
    const response = {
        statusCode: 200,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        },
        body: JSON.stringify({
            message: 'GameFlex Posts Service',
            path: path,
            method: method,
            timestamp: new Date().toISOString(),
            environment: 'development',
            samplePosts: [
                {
                    id: '1',
                    content: 'Welcome to GameFlex!',
                    author: 'system',
                    created_at: new Date().toISOString()
                }
            ]
        })
    };
    
    return response;
};
"@
        },
        @{
            Name    = "gameflex-media-development"
            Handler = "index.handler"
            Code    = @"
exports.handler = async (event, context) => {
    console.log('Media function called with event:', JSON.stringify(event));
    
    const path = event.path || event.rawPath || '';
    const method = event.httpMethod || event.requestContext?.http?.method || 'GET';
    
    const response = {
        statusCode: 200,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        },
        body: JSON.stringify({
            message: 'GameFlex Media Service',
            path: path,
            method: method,
            timestamp: new Date().toISOString(),
            environment: 'development',
            supportedFormats: ['jpg', 'png', 'gif', 'mp4', 'webm']
        })
    };
    
    return response;
};
"@
        }
    )
    
    foreach ($func in $functions) {
        try {
            # Create temporary files
            $tempDir = New-TemporaryFile | ForEach-Object { Remove-Item $_; New-Item -ItemType Directory -Path $_ }
            $codeFile = Join-Path $tempDir "index.js"
            $zipFile = Join-Path $tempDir "function.zip"
            
            $func.Code | Out-File -FilePath $codeFile -Encoding UTF8
            Compress-Archive -Path $codeFile -DestinationPath $zipFile
            
            # Create or update Lambda function
            $existingFunction = aws --endpoint-url=$ENDPOINT_URL lambda get-function --function-name $func.Name 2>$null
            
            if ($LASTEXITCODE -eq 0) {
                # Update existing function
                aws --endpoint-url=$ENDPOINT_URL lambda update-function-code --function-name $func.Name --zip-file "fileb://$zipFile" | Out-Null
                Write-Status "Updated Lambda function: $($func.Name)"
            }
            else {
                # Create new function
                aws --endpoint-url=$ENDPOINT_URL lambda create-function `
                    --function-name $func.Name `
                    --runtime "nodejs18.x" `
                    --role "arn:aws:iam::123456789012:role/$roleName" `
                    --handler $func.Handler `
                    --zip-file "fileb://$zipFile" | Out-Null
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Status "Created Lambda function: $($func.Name)"
                }
                else {
                    Write-Warning "Failed to create Lambda function: $($func.Name)"
                }
            }
            
            # Clean up
            Remove-Item $tempDir -Recurse -Force
        }
        catch {
            Write-Error "Failed to create Lambda function $($func.Name): $_"
        }
    }
}

# Test Lambda functions
function Test-LambdaFunctions {
    Write-Status "Testing Lambda functions..."
    
    $functions = @("gameflex-auth-development", "gameflex-posts-development", "gameflex-media-development")
    
    foreach ($functionName in $functions) {
        try {
            $testEvent = @{
                path       = "/test"
                httpMethod = "GET"
                headers    = @{}
                body       = $null
            } | ConvertTo-Json -Depth 10
            
            $testEvent | Out-File -FilePath "temp-event.json" -Encoding UTF8
            
            $result = aws --endpoint-url=$ENDPOINT_URL lambda invoke `
                --function-name $functionName `
                --payload file://temp-event.json `
                "response-$functionName.json" 2>$null
            
            if ($LASTEXITCODE -eq 0 -and (Test-Path "response-$functionName.json")) {
                $response = Get-Content "response-$functionName.json" | ConvertFrom-Json
                Write-Status "✓ ${functionName}: $($response.message)"
                Remove-Item "response-$functionName.json" -Force
            }
            else {
                Write-Warning "✗ Failed to invoke ${functionName}"
            }
            
            Remove-Item "temp-event.json" -Force -ErrorAction SilentlyContinue
        }
        catch {
            Write-Warning "Error testing ${functionName}: $_"
        }
    }
}

# Display deployment information
function Show-DeploymentInfo {
    Write-Host ""
    Write-Header "Simple Deployment completed successfully!"
    Write-Host ""
    
    Write-Status "Deployed Services:"
    Write-Host "  📦 S3 Buckets: gameflex-media-development, gameflex-avatars-development, gameflex-temp-development" -ForegroundColor Cyan
    Write-Host "  ⚡ Lambda Functions: gameflex-auth-development, gameflex-posts-development, gameflex-media-development" -ForegroundColor Cyan
    Write-Host "  🗄️  Database: PostgreSQL with seed data (5 users)" -ForegroundColor Cyan
    Write-Host "  🔴 Cache: Redis" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Status "Service URLs:"
    Write-Host "  🌐 LocalStack: http://localhost:45660/_localstack/health" -ForegroundColor Cyan
    Write-Host "  📦 S3 Console: http://localhost:45660/_aws/s3" -ForegroundColor Cyan
    Write-Host "  ⚡ Lambda Console: http://localhost:45660/_aws/lambda" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Status "Test Commands:"
    Write-Host "  📊 Health Check: .\health-check.ps1" -ForegroundColor Cyan
    Write-Host "  🧪 Simple Test: .\test-simple.ps1" -ForegroundColor Cyan
    Write-Host "  🗄️  Database: .\manage-database.ps1 status" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Warning "Note: This uses LocalStack free tier. Cognito and API Gateway are not available."
    Write-Status "For production, deploy to real AWS services with full functionality."
}

# Main deployment function
function Main {
    Write-Header "GameFlex Simple AWS Deployment"
    Write-Host ""
    
    # Test connection
    if (-not (Test-AwsConnection)) {
        Write-Error "Cannot connect to LocalStack. Make sure it's running with .\start.ps1"
        exit 1
    }
    
    # Deploy services
    New-S3Buckets
    New-LambdaFunctions
    Test-LambdaFunctions
    
    # Show results
    Show-DeploymentInfo
    
    Write-Status "Simple deployment completed successfully!"
}

# Show help if requested
if ($args -contains "-h" -or $args -contains "--help") {
    Write-Host "GameFlex Simple Deployment Script" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Usage: .\deploy-simple.ps1 [OPTIONS]" -ForegroundColor Green
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Environment    Environment name (default: development)"
    Write-Host "  -ProjectName    Project name (default: gameflex)"
    Write-Host "  -Force          Force deployment even if validation fails"
    Write-Host "  -Verbose        Show verbose output"
    Write-Host "  -h, --help      Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\deploy-simple.ps1"
    Write-Host "  .\deploy-simple.ps1 -Environment staging"
    Write-Host "  .\deploy-simple.ps1 -Verbose"
    exit 0
}

# Run deployment
try {
    Main
}
catch {
    Write-Error "Deployment failed: $_"
    exit 1
}
