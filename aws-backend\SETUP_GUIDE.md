# GameFlex AWS Backend Setup Guide

This guide will help you set up and run the GameFlex AWS backend using LocalStack for development.

## Prerequisites

### Required Software
- **Docker Desktop** (latest version)
- **PowerShell** 5.1 or later
- **AWS CLI** v2 (for testing and management)
- **PostgreSQL Client Tools** (psql command)
- **Python 3.11+** (for Lambda function development)

### System Requirements
- **RAM**: 6GB+ available for Docker
- **Storage**: 2GB+ free space
- **Ports**: 4566, 5433, 6380 must be available

## Installation Steps

### 1. Install Prerequisites

#### Docker Desktop
1. Download from [docker.com](https://www.docker.com/products/docker-desktop)
2. Install and start Docker Desktop
3. Allocate at least 6GB RAM to Docker in settings

#### AWS CLI
```powershell
# Using winget (Windows 11)
winget install Amazon.AWSCLI

# Or download from: https://aws.amazon.com/cli/
```

#### PostgreSQL Client
```powershell
# Using winget
winget install PostgreSQL.PostgreSQL

# Or download from: https://www.postgresql.org/download/windows/
```

### 2. Clone and Setup

```powershell
# Navigate to your project directory
cd path\to\gameflex_mobile\aws-backend

# Verify all files are present
ls
```

### 3. Configure Environment

```powershell
# Copy environment template (if needed)
cp .env.example .env

# Edit .env file with your preferred settings
notepad .env
```

## Quick Start

### 1. Start the Backend

```powershell
# Start all services
.\start.ps1

# Wait for all services to be ready (this may take 2-3 minutes)
```

### 2. Deploy Infrastructure

```powershell
# Deploy AWS infrastructure to LocalStack
.\deploy-infrastructure.ps1

# This will create:
# - Cognito User Pool and Identity Pool
# - S3 buckets for media storage
# - Lambda functions
# - API Gateway with endpoints
```

### 3. Test the Backend

```powershell
# Run comprehensive tests
.\test-backend.ps1

# Run with verbose output
.\test-backend.ps1 -Verbose
```

## Service URLs

Once running, you can access:

- **LocalStack Dashboard**: http://localhost:4566/_localstack/health
- **API Gateway**: http://localhost:4566/restapis/{api-id}/development/_user_request_
- **S3 Console**: http://localhost:4566/_aws/s3
- **PostgreSQL**: localhost:5433 (user: postgres, password: gameflex_password)
- **Redis**: localhost:6380

## API Endpoints

### Authentication
- `POST /auth/signup` - User registration
- `POST /auth/signin` - User login
- `POST /auth/refresh` - Refresh access token
- `POST /auth/signout` - User logout

### Posts
- `GET /posts` - List posts (with pagination)
- `POST /posts` - Create new post (requires auth)
- `GET /posts/{id}` - Get specific post

### Media
- `POST /media/upload` - Upload media file (requires auth)
- `GET /media/{id}` - Get media metadata

## Development Workflow

### 1. Making Changes to Lambda Functions

```powershell
# After modifying lambda-functions/auth/handler.py
.\deploy-infrastructure.ps1

# This will package and update the Lambda function
```

### 2. Database Management

```powershell
# Check database status
.\manage-database.ps1 status

# Reset database (WARNING: deletes all data)
.\manage-database.ps1 reset -Force

# Create backup
.\manage-database.ps1 backup my_backup.sql

# Restore from backup
.\manage-database.ps1 restore my_backup.sql
```

### 3. Testing Individual Components

```powershell
# Test specific endpoints
curl -X GET "http://localhost:4566/restapis/{api-id}/development/_user_request_/posts"

# Test with authentication
$token = "your-access-token"
curl -X POST "http://localhost:4566/restapis/{api-id}/development/_user_request_/posts" `
  -H "Authorization: Bearer $token" `
  -H "Content-Type: application/json" `
  -d '{"content": "Test post"}'
```

## Troubleshooting

### Common Issues

#### 1. LocalStack Not Starting
```powershell
# Check Docker memory allocation
docker system info

# Restart Docker Desktop
# Increase memory allocation to 6GB+
```

#### 2. Port Conflicts
```powershell
# Check what's using the ports
netstat -an | findstr "4566\|5433\|6380"

# Stop conflicting services or change ports in docker-compose.yml
```

#### 3. Lambda Function Deployment Fails
```powershell
# Check function package size
ls -la packages\*.zip

# Ensure dependencies are correctly installed
pip install -r lambda-functions\auth\requirements.txt
```

#### 4. Database Connection Issues
```powershell
# Test database connection
.\manage-database.ps1 status

# Check if PostgreSQL container is running
docker compose ps postgres
```

#### 5. API Gateway Not Responding
```powershell
# Get API Gateway ID
aws --endpoint-url=http://localhost:4566 apigateway get-rest-apis

# Check CloudFormation stack status
aws --endpoint-url=http://localhost:4566 cloudformation describe-stacks --stack-name gameflex-infrastructure-development
```

### Logs and Debugging

```powershell
# View all service logs
docker compose logs -f

# View specific service logs
docker compose logs -f localstack
docker compose logs -f postgres

# View Lambda function logs (in LocalStack)
aws --endpoint-url=http://localhost:4566 logs describe-log-groups
```

## Development Tips

### 1. Environment Variables
- All configuration is in `.env` file
- Lambda functions inherit environment variables from CloudFormation
- Database credentials are consistent across all services

### 2. Hot Reloading
- Lambda functions require redeployment after changes
- Database schema changes need manual migration
- Static files (S3) are immediately available

### 3. Testing Strategy
- Use `test-backend.ps1` for comprehensive testing
- Test individual endpoints with curl or Postman
- Use database management script for data verification

### 4. Performance Optimization
- LocalStack can be slow on first startup
- Use `PERSISTENCE=1` to maintain state between restarts
- Consider using LocalStack Pro for better performance

## Production Considerations

⚠️ **This setup is for development only!**

For production deployment:
1. Replace LocalStack with real AWS services
2. Use proper authentication and authorization
3. Implement proper error handling and logging
4. Set up monitoring and alerting
5. Use environment-specific configurations
6. Implement proper backup and disaster recovery

## Support

For issues with this setup:
1. Check the logs: `docker compose logs`
2. Verify services are running: `docker compose ps`
3. Run the test suite: `.\test-backend.ps1`
4. Check LocalStack documentation: https://docs.localstack.cloud/
5. Review AWS service documentation for specific issues

## Next Steps

1. **Integrate with Flutter App**: Update your Flutter app to use the new AWS backend endpoints
2. **Add More Features**: Implement channels, likes, comments, and other features
3. **Improve Security**: Add proper authentication middleware and input validation
4. **Add Monitoring**: Implement logging and monitoring for production readiness
5. **Performance Testing**: Test with larger datasets and concurrent users
