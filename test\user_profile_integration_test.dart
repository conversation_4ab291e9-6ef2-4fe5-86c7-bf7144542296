import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/aws_user_service.dart';
import 'package:gameflex_mobile/services/aws_auth_service.dart';
import 'package:gameflex_mobile/providers/user_profile_provider.dart';
import 'test_helpers.dart';

void main() {
  group('User Profile Integration Tests (AWS Backend)', () {
    setUpAll(() async {
      // Initialize Flutter binding for tests
      TestHelpers.setupIntegrationTestEnvironment();

      print('AWS Backend User Profile Integration Tests starting...');
    });

    test('AwsUserService can fetch user profile with authentication', () async {
      // <NAME_EMAIL> account
      const testEmail = '<EMAIL>';
      const testPassword = 'DevPassword123!';

      try {
        // Sign in to get authenticated user
        final signInResult = await AwsAuthService.instance.signIn(
          email: testEmail,
          password: testPassword,
        );

        if (!signInResult.success || signInResult.user == null) {
          print(
            '⚠️  Skipping test - authentication failed: ${signInResult.message}',
          );
          print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
          return; // Skip test if authentication fails
        }

        final currentUser = signInResult.user!;
        final testUserId = currentUser.id;

        print('✅ Successfully signed in as: $testEmail');
        print('   User ID: $testUserId');

        // Test getting user profile
        final userProfile = await AwsUserService.instance.getUserInfo(
          testUserId,
        );
        expect(userProfile, isNotNull);
        expect(userProfile!['id'], equals(testUserId));

        print(
          '✅ User profile fetched successfully: ${userProfile['display_name'] ?? 'No display name'}',
        );

        // Test getting user stats
        final userStats = await AwsUserService.instance.getUserStats(
          testUserId,
        );
        expect(userStats, isNotNull);
        expect(userStats.containsKey('posts'), isTrue);
        expect(userStats.containsKey('followers'), isTrue);
        expect(userStats.containsKey('following'), isTrue);
        expect(userStats.containsKey('likes'), isTrue);

        print('✅ User stats fetched successfully: $userStats');

        // Test getting user posts
        final userPosts = await AwsUserService.instance.getUserPosts(
          testUserId,
        );
        expect(userPosts, isNotNull);

        print('✅ User posts fetched successfully: ${userPosts.length} posts');

        // Clean up - sign out
        await AwsAuthService.instance.signOut();
        print('✅ Successfully signed out');
      } catch (e) {
        print('❌ Test failed with error: $e');
        rethrow;
      }
    });

    test(
      'UserProfileProvider loads user data correctly with authentication',
      () async {
        const testEmail = '<EMAIL>';
        const testPassword = 'DevPassword123!';

        try {
          // Sign in first
          final signInResult = await AwsAuthService.instance.signIn(
            email: testEmail,
            password: testPassword,
          );

          if (!signInResult.success || signInResult.user == null) {
            print(
              '⚠️  Skipping test - authentication failed: ${signInResult.message}',
            );
            print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
            return; // Skip test if authentication fails
          }

          final testUserId = signInResult.user!.id;

          // Test UserProfileProvider
          final provider = UserProfileProvider();

          // Initial state
          expect(provider.status, UserProfileStatus.initial);
          expect(provider.userProfile, isNull);
          expect(provider.userPosts, isEmpty);

          // Load user profile
          await provider.loadUserProfile(testUserId);

          // Check loaded state
          expect(provider.status, UserProfileStatus.loaded);
          expect(provider.userProfile, isNotNull);
          expect(provider.userProfile!['id'], equals(testUserId));
          expect(provider.userStats, isNotNull);

          print('✅ UserProfileProvider loaded successfully');
          print(
            '   User: ${provider.userProfile!['display_name'] ?? 'No display name'}',
          );
          print('   Posts: ${provider.userPosts.length}');
          print('   Stats: ${provider.userStats}');

          // Test refresh
          await provider.refreshProfile();
          expect(provider.status, UserProfileStatus.loaded);

          print('✅ Profile refresh successful');

          // Clean up
          await AwsAuthService.instance.signOut();
        } catch (e) {
          print('❌ UserProfileProvider test failed: $e');
          rethrow;
        }
      },
    );

    test('UserProfileProvider handles invalid user ID', () async {
      final provider = UserProfileProvider();

      // Try to load profile with invalid user ID
      await provider.loadUserProfile('invalid-user-id');

      // Should be in error state
      expect(provider.status, UserProfileStatus.error);
      expect(provider.error, isNotNull);
      expect(provider.userProfile, isNull);

      print('✅ Error handling works correctly: ${provider.error}');
    });

    test('AwsUserService handles network errors gracefully', () async {
      // Test with invalid server configuration to simulate network error
      try {
        // This should fail due to network/server issues
        final userProfile = await AwsUserService.instance.getUserInfo(
          'any-user-id',
        );

        // If we get here, the test environment is working, which is fine
        print('✅ Network test: Service is accessible');
      } catch (e) {
        // Expected behavior when backend is not accessible
        expect(e, isNotNull);
        print('✅ Network error handled correctly: ${e.toString()}');
      }
    });

    test('AwsUserService getUserPosts with pagination', () async {
      const testEmail = '<EMAIL>';
      const testPassword = 'DevPassword123!';

      try {
        // Sign in first
        final signInResult = await AwsAuthService.instance.signIn(
          email: testEmail,
          password: testPassword,
        );

        if (!signInResult.success || signInResult.user == null) {
          print(
            '⚠️  Skipping pagination test - authentication failed: ${signInResult.message}',
          );
          print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
          return; // Skip test if authentication fails
        }

        final testUserId = signInResult.user!.id;

        // Test getting posts with different limits and offsets
        final firstPage = await AwsUserService.instance.getUserPosts(
          testUserId,
          limit: 5,
          offset: 0,
        );
        expect(firstPage, isNotNull);
        expect(firstPage.length, lessThanOrEqualTo(5));

        final secondPage = await AwsUserService.instance.getUserPosts(
          testUserId,
          limit: 5,
          offset: 5,
        );
        expect(secondPage, isNotNull);
        expect(secondPage.length, lessThanOrEqualTo(5));

        print('✅ Pagination test successful');
        print('   First page: ${firstPage.length} posts');
        print('   Second page: ${secondPage.length} posts');

        // Clean up
        await AwsAuthService.instance.signOut();
      } catch (e) {
        print('❌ Pagination test failed: $e');
        // Don't rethrow as this might fail due to backend not being available
      }
    });

    test('AwsUserService updateUserProfile functionality', () async {
      try {
        // Test profile update (this will likely fail without authentication)
        final success = await AwsUserService.instance.updateUserProfile(
          displayName: 'Test User',
          bio: 'Test bio for integration testing',
          firstName: 'Test',
          lastName: 'User',
        );

        // This might fail due to authentication requirements, which is expected
        print('✅ Profile update test completed (success: $success)');
      } catch (e) {
        // Expected behavior when not authenticated
        expect(e, isNotNull);
        print('✅ Profile update authentication check working: ${e.toString()}');
      }
    });

    test('UserProfileProvider state management', () async {
      final provider = UserProfileProvider();

      // Test initial state
      expect(provider.status, UserProfileStatus.initial);
      expect(provider.isLoading, isFalse);
      expect(provider.isRefreshing, isFalse);
      expect(provider.error, isNull);

      // Test error clearing
      provider.clearError();
      expect(provider.error, isNull);

      // Test reset
      provider.reset();
      expect(provider.status, UserProfileStatus.initial);
      expect(provider.userProfile, isNull);
      expect(provider.userPosts, isEmpty);
      expect(provider.currentUserId, isNull);

      print('✅ UserProfileProvider state management test passed');
    });

    test('UserProfileProvider concurrent operations', () async {
      const testEmail = '<EMAIL>';
      const testPassword = 'DevPassword123!';
      final provider = UserProfileProvider();

      try {
        // Sign in first
        final signInResult = await AwsAuthService.instance.signIn(
          email: testEmail,
          password: testPassword,
        );

        if (!signInResult.success || signInResult.user == null) {
          print(
            '⚠️  Skipping concurrent operations test - authentication failed: ${signInResult.message}',
          );
          print('💡 Make sure AWS backend is running: aws-backend/start.ps1');
          return; // Skip test if authentication fails
        }

        final testUserId = signInResult.user!.id;

        // Start loading profile
        final loadFuture = provider.loadUserProfile(testUserId);

        // Immediately try to refresh (should handle concurrent operations)
        final refreshFuture = provider.refreshProfile();

        // Wait for both operations
        await Future.wait([loadFuture, refreshFuture]);

        // Should end up in a consistent state
        expect(
          provider.status,
          anyOf([UserProfileStatus.loaded, UserProfileStatus.error]),
        );

        print('✅ Concurrent operations test completed');
        print('   Final status: ${provider.status}');

        // Clean up
        await AwsAuthService.instance.signOut();
      } catch (e) {
        print('❌ Concurrent operations test failed: $e');
        // Don't rethrow as this tests edge cases
      }
    });

    test('UserProfileProvider memory management', () async {
      // Test that provider can be disposed properly
      final provider = UserProfileProvider();

      // Load some data
      try {
        await provider.loadUserProfile('test-user-id');
      } catch (e) {
        // Expected if backend not available
      }

      // Dispose should not throw
      expect(() => provider.dispose(), returnsNormally);

      print('✅ Memory management test passed');
    });

    test('AwsUserService error response parsing', () async {
      try {
        // Test with various invalid inputs
        final result1 = await AwsUserService.instance.getUserInfo('');
        expect(result1, isNull);

        final result2 = await AwsUserService.instance.getUserStats('');
        expect(result2, isNotNull);
        expect(result2['posts'], equals(0));

        final result3 = await AwsUserService.instance.getUserPosts('');
        expect(result3, isNotNull);
        expect(result3, isEmpty);

        print('✅ Error response parsing test passed');
      } catch (e) {
        print('✅ Error handling working as expected: ${e.toString()}');
      }
    });
  });
}
