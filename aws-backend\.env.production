# GameFlex AWS Backend Environment Configuration - Production
# This file contains production-specific environment variables

############
# Environment Configuration
############
APP_ENVIRONMENT=production
NODE_ENV=production
DEBUG=false

############
# AWS Configuration - Real AWS
############
AWS_DEFAULT_REGION=us-east-1
# AWS credentials should be configured via AWS CLI profiles
# or IAM roles, not stored in this file
AWS_PROFILE=production

############
# DynamoDB Configuration
############
# DynamoDB configuration for production environment
DYNAMODB_REGION=us-east-1
# DynamoDB credentials should be configured via AWS CLI profiles
# or IAM roles, not stored in this file

# DynamoDB Table Names (with environment prefix)
DYNAMODB_TABLE_USERS=gameflex-production-Users
DYNAMODB_TABLE_USER_PROFILES=gameflex-production-UserProfiles
DYNAMODB_TABLE_CHANNELS=gameflex-production-Channels
DYNAMODB_TABLE_CHANNEL_MEMBERS=gameflex-production-ChannelMembers
DYNAMODB_TABLE_MEDIA=gameflex-production-Media
DYNAMODB_TABLE_POSTS=gameflex-production-Posts
DYNAMODB_TABLE_COMMENTS=gameflex-production-Comments
DYNAMODB_TABLE_LIKES=gameflex-production-Likes
DYNAMODB_TABLE_FOLLOWS=gameflex-production-Follows
DYNAMODB_TABLE_NOTIFICATIONS=gameflex-production-Notifications

############
# Cognito Configuration (Populated from CloudFormation outputs)
############
COGNITO_USER_POOL_ID=
COGNITO_USER_POOL_CLIENT_ID=
COGNITO_IDENTITY_POOL_ID=

############
# S3 Configuration
############
S3_BUCKET_MEDIA=gameflex-media-production
S3_BUCKET_AVATARS=gameflex-avatars-production
S3_BUCKET_TEMP=gameflex-temp-production

############
# API Gateway Configuration
############
API_GATEWAY_URL=https://api.gameflex.io
API_GATEWAY_STAGE=production

############
# Lambda Configuration
############
LAMBDA_RUNTIME=nodejs18.x
LAMBDA_TIMEOUT=60
LAMBDA_MEMORY_SIZE=1024

############
# Application Configuration
############
APP_NAME=GameFlex
APP_VERSION=1.0.0
PROJECT_NAME=gameflex

# JWT Configuration
# JWT_SECRET should be retrieved from AWS Secrets Manager
JWT_SECRET=
JWT_EXPIRY=3600
JWT_REFRESH_EXPIRY=604800

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://gameflex.io,https://www.gameflex.io,https://api.gameflex.io
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token

############
# File Upload Configuration
############
MAX_FILE_SIZE=52428800  # 50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mov,avi
UPLOAD_PATH=uploads

############
# Email Configuration (for Cognito)
############
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

############
# Logging Configuration
############
LOG_LEVEL=ERROR
LOG_FORMAT=json
ENABLE_DEBUG_LOGS=false

############
# Security Configuration
############
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=3
LOCKOUT_DURATION=1800

############
# Production Configuration
############
ENABLE_CORS=true
ENABLE_SWAGGER=false
ENABLE_HOT_RELOAD=false
ENABLE_MOCK_DATA=false

############
# Monitoring and Alerting
############
ENABLE_METRICS=true
ENABLE_TRACING=true
ENABLE_ALERTS=true
ALERTING_EMAIL=<EMAIL>

############
# Performance Configuration
############
ENABLE_CACHING=true
CACHE_TTL=3600
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100

############
# Feature Flags
############
FEATURE_USER_REGISTRATION=true
FEATURE_SOCIAL_LOGIN=true
FEATURE_PUSH_NOTIFICATIONS=true
FEATURE_ANALYTICS=true

############
# Production Security
############
ENABLE_WAF=true
ENABLE_CLOUDFRONT=true
ENABLE_ENCRYPTION=true
ENABLE_BACKUP=true
ENABLE_MONITORING=true
