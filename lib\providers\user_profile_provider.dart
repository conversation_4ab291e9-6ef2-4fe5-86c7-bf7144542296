import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;
import '../services/aws_user_service.dart';
import '../models/post_model.dart';
import '../services/aws_posts_service.dart';

/// User profile status enumeration
enum UserProfileStatus {
  initial,
  loading,
  loaded,
  refreshing,
  error,
}

/// Provider for managing user profile state
class UserProfileProvider extends ChangeNotifier {
  UserProfileStatus _status = UserProfileStatus.initial;
  Map<String, dynamic>? _userProfile;
  Map<String, dynamic>? _userStats;
  List<PostModel> _userPosts = [];
  String? _errorMessage;
  String? _currentUserId;

  // Getters
  UserProfileStatus get status => _status;
  Map<String, dynamic>? get userProfile => _userProfile;
  Map<String, dynamic>? get userStats => _userStats;
  List<PostModel> get userPosts => _userPosts;
  String? get error => _errorMessage;
  String? get currentUserId => _currentUserId;
  bool get isLoading => _status == UserProfileStatus.loading;
  bool get isRefreshing => _status == UserProfileStatus.refreshing;

  /// Load user profile data
  Future<void> loadUserProfile(String userId) async {
    if (_status == UserProfileStatus.loading) return;

    developer.log('UserProfileProvider: Loading profile for user: $userId');
    if (kDebugMode) {
      print('UserProfileProvider: Loading profile for user: $userId');
    }

    _currentUserId = userId;
    _setStatus(UserProfileStatus.loading);

    try {
      // Get user profile info (includes user, profile, and stats)
      final userInfo = await AwsUserService.instance.getUserInfo(userId);
      
      if (userInfo != null) {
        _userProfile = userInfo;
        _userStats = userInfo['stats'] as Map<String, dynamic>?;
        
        developer.log('UserProfileProvider: Successfully loaded user profile');
        if (kDebugMode) {
          print('UserProfileProvider: Successfully loaded user profile');
        }
      } else {
        throw Exception('User profile not found');
      }

      // Load user posts
      await _loadUserPosts(userId);

      _setStatus(UserProfileStatus.loaded);
      developer.log('UserProfileProvider: Profile loading completed');
      if (kDebugMode) {
        print('UserProfileProvider: Profile loading completed');
      }
    } catch (e, stackTrace) {
      developer.log(
        'UserProfileProvider: Error loading profile',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('UserProfileProvider ERROR: $e');
        print('UserProfileProvider STACK TRACE: $stackTrace');
      }
      _setError('Failed to load user profile: $e');
    }
  }

  /// Load user posts
  Future<void> _loadUserPosts(String userId) async {
    try {
      developer.log('UserProfileProvider: Loading posts for user: $userId');
      
      final postsData = await AwsUserService.instance.getUserPosts(userId);
      
      // Convert to PostModel objects
      _userPosts = postsData.map((postData) {
        try {
          return PostModel.fromJson(postData);
        } catch (e) {
          developer.log('UserProfileProvider: Error parsing post: $e');
          return null;
        }
      }).where((post) => post != null).cast<PostModel>().toList();

      developer.log('UserProfileProvider: Loaded ${_userPosts.length} posts');
      if (kDebugMode) {
        print('UserProfileProvider: Loaded ${_userPosts.length} posts');
      }
    } catch (e) {
      developer.log('UserProfileProvider: Error loading user posts: $e');
      if (kDebugMode) {
        print('UserProfileProvider: Error loading user posts: $e');
      }
      // Don't fail the entire profile load if posts fail
      _userPosts = [];
    }
  }

  /// Refresh user profile data
  Future<void> refreshProfile() async {
    if (_currentUserId == null) return;
    
    developer.log('UserProfileProvider: Refreshing profile');
    if (kDebugMode) {
      print('UserProfileProvider: Refreshing profile');
    }

    _setStatus(UserProfileStatus.refreshing);

    try {
      // Reload user profile info
      final userInfo = await AwsUserService.instance.getUserInfo(_currentUserId!);
      
      if (userInfo != null) {
        _userProfile = userInfo;
        _userStats = userInfo['stats'] as Map<String, dynamic>?;
      }

      // Reload user posts
      await _loadUserPosts(_currentUserId!);

      _setStatus(UserProfileStatus.loaded);
      developer.log('UserProfileProvider: Profile refresh completed');
      if (kDebugMode) {
        print('UserProfileProvider: Profile refresh completed');
      }
    } catch (e, stackTrace) {
      developer.log(
        'UserProfileProvider: Error refreshing profile',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('UserProfileProvider REFRESH ERROR: $e');
        print('UserProfileProvider REFRESH STACK TRACE: $stackTrace');
      }
      _setError('Failed to refresh user profile: $e');
    }
  }

  /// Update user profile
  Future<bool> updateProfile({
    String? displayName,
    String? username,
    String? bio,
    String? avatarUrl,
    String? firstName,
    String? lastName,
    String? country,
    String? timezone,
    String? language,
  }) async {
    try {
      developer.log('UserProfileProvider: Updating profile');
      if (kDebugMode) {
        print('UserProfileProvider: Updating profile');
      }

      final success = await AwsUserService.instance.updateUserProfile(
        displayName: displayName,
        username: username,
        bio: bio,
        avatarUrl: avatarUrl,
        firstName: firstName,
        lastName: lastName,
        country: country,
        timezone: timezone,
        language: language,
      );

      if (success && _currentUserId != null) {
        // Refresh the profile data after successful update
        await refreshProfile();
        
        developer.log('UserProfileProvider: Profile updated successfully');
        if (kDebugMode) {
          print('UserProfileProvider: Profile updated successfully');
        }
        return true;
      } else {
        throw Exception('Failed to update profile');
      }
    } catch (e) {
      developer.log('UserProfileProvider: Error updating profile: $e');
      if (kDebugMode) {
        print('UserProfileProvider: Error updating profile: $e');
      }
      _setError('Failed to update profile: $e');
      return false;
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    if (_status == UserProfileStatus.error) {
      _setStatus(UserProfileStatus.loaded);
    }
  }

  /// Reset provider state
  void reset() {
    _status = UserProfileStatus.initial;
    _userProfile = null;
    _userStats = null;
    _userPosts.clear();
    _errorMessage = null;
    _currentUserId = null;
    notifyListeners();
  }

  /// Set status and notify listeners
  void _setStatus(UserProfileStatus status) {
    _status = status;
    if (status != UserProfileStatus.error) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Set error and notify listeners
  void _setError(String error) {
    developer.log('UserProfileProvider: Setting error: $error');
    if (kDebugMode) {
      print('UserProfileProvider: ERROR SET: $error');
    }
    _errorMessage = error;
    _status = UserProfileStatus.error;
    notifyListeners();
  }
}
