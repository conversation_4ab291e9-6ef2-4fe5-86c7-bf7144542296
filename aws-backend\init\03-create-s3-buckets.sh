#!/bin/bash

# GameFlex S3 Buckets Creation Script
# This script creates S3 buckets and uploads initial media files
# Runs automatically when LocalStack starts

set -e

echo "[S3] Creating S3 buckets for GameFlex..."

# S3 bucket names (matching the Lambda function configuration)
MEDIA_BUCKET="gameflex-media-development"
AVATARS_BUCKET="gameflex-avatars-development"
TEMP_BUCKET="gameflex-temp-development"
CLIPART_BUCKET="gameflex-clipart-development"

# Function to create an S3 bucket
create_bucket() {
    local bucket_name=$1
    
    echo "[INFO] Creating S3 bucket: $bucket_name"
    
    # Check if bucket exists
    if awslocal s3api head-bucket --bucket "$bucket_name" >/dev/null 2>&1; then
        echo "[INFO] Bucket $bucket_name already exists"
        return 0
    fi
    
    # Create bucket
    awslocal s3api create-bucket --bucket "$bucket_name"
    
    # Set bucket policy to allow public read access for media files
    awslocal s3api put-bucket-policy --bucket "$bucket_name" --policy '{
        "Version": "2012-10-17",
        "Statement": [
            {
                "Sid": "PublicReadGetObject",
                "Effect": "Allow",
                "Principal": "*",
                "Action": "s3:GetObject",
                "Resource": "arn:aws:s3:::'$bucket_name'/*"
            }
        ]
    }'
    
    echo "[INFO] Bucket $bucket_name created successfully"
}

# Function to upload files to S3
upload_files() {
    local source_dir=$1
    local bucket_name=$2
    local s3_prefix=$3
    
    if [ ! -d "$source_dir" ]; then
        echo "[WARN] Source directory $source_dir does not exist, skipping upload"
        return 0
    fi
    
    echo "[INFO] Uploading files from $source_dir to s3://$bucket_name/$s3_prefix"
    
    # Upload all files recursively
    find "$source_dir" -type f | while read -r file; do
        # Get relative path from source directory
        relative_path=$(realpath --relative-to="$source_dir" "$file")
        s3_key="$s3_prefix/$relative_path"
        
        echo "[INFO] Uploading $file to s3://$bucket_name/$s3_key"
        
        # Determine content type based on file extension
        case "${file##*.}" in
            jpg|jpeg) content_type="image/jpeg" ;;
            png) content_type="image/png" ;;
            gif) content_type="image/gif" ;;
            webp) content_type="image/webp" ;;
            mp4) content_type="video/mp4" ;;
            mov) content_type="video/quicktime" ;;
            avi) content_type="video/x-msvideo" ;;
            webm) content_type="video/webm" ;;
            *) content_type="application/octet-stream" ;;
        esac
        
        awslocal s3api put-object \
            --bucket "$bucket_name" \
            --key "$s3_key" \
            --body "$file" \
            --content-type "$content_type"
    done
}

# Create all S3 buckets
echo "[INFO] Creating S3 buckets..."
create_bucket "$MEDIA_BUCKET"
create_bucket "$AVATARS_BUCKET"
create_bucket "$TEMP_BUCKET"
create_bucket "$CLIPART_BUCKET"

# Upload clipart images
echo "[INFO] Uploading clipart images..."
upload_files "/opt/assets/images/clipart" "$CLIPART_BUCKET" "clipart"

# Upload sample media files for posts
echo "[INFO] Uploading sample media files for posts..."

# Create sample image files if they don't exist
create_sample_image() {
    local filename=$1
    local s3_key=$2
    local width=${3:-1080}
    local height=${4:-1920}

    # Create a simple colored rectangle as sample image using ImageMagick if available
    # Otherwise create a placeholder text file
    if command -v convert >/dev/null 2>&1; then
        # Create a temporary image with ImageMagick
        local temp_file="/tmp/$filename"
        convert -size ${width}x${height} xc:"#$(printf '%06x' $((RANDOM % 16777216)))" \
                -gravity center -pointsize 72 -fill white \
                -annotate +0+0 "GameFlex\nSample\nImage" \
                "$temp_file"

        awslocal s3api put-object \
            --bucket "$MEDIA_BUCKET" \
            --key "$s3_key" \
            --body "$temp_file" \
            --content-type "image/jpeg"

        rm -f "$temp_file"
        echo "[INFO] Created sample image: $s3_key"
    else
        # Create a placeholder text file
        local temp_file="/tmp/$filename.txt"
        echo "GameFlex Sample Image - $filename" > "$temp_file"

        awslocal s3api put-object \
            --bucket "$MEDIA_BUCKET" \
            --key "$s3_key" \
            --body "$temp_file" \
            --content-type "text/plain"

        rm -f "$temp_file"
        echo "[INFO] Created placeholder file: $s3_key"
    fi
}

# Upload existing media files from assets
upload_post_media() {
    local source_file=$1
    local s3_key=$2
    local content_type=$3

    if [ -f "$source_file" ]; then
        awslocal s3api put-object \
            --bucket "$MEDIA_BUCKET" \
            --key "$s3_key" \
            --body "$source_file" \
            --content-type "$content_type"
        echo "[INFO] Uploaded media file: $s3_key"
    else
        echo "[WARN] Source file not found: $source_file"
    fi
}

# Upload the two existing images for posts
upload_post_media "/opt/assets/media/cod_screenshot.jpg" "user/00000000-0000-0000-0000-000000000003/cod_screenshot.jpg" "image/jpeg"
upload_post_media "/opt/assets/media/diablo_screenshot.webp" "user/00000000-0000-0000-0000-000000000004/diablo_screenshot.webp" "image/webp"

echo "[S3] S3 buckets and media files setup completed successfully!"
