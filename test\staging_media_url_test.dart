import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/config_service.dart';

void main() {
  group('Staging Configuration Tests (AWS Backend)', () {
    late ConfigService configService;

    setUpAll(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
      configService = ConfigService.instance;
    });

    test(
      'Configuration should use staging settings when STAGING=true',
      () async {
        // This test should be run with: flutter test --dart-define=STAGING=true
        const isStaging = bool.fromEnvironment('STAGING', defaultValue: false);

        if (!isStaging) {
          // Skip this test if not running in staging mode
          return;
        }

        // Test configuration for staging
        final serverUrl = await configService.getServerUrl();
        final environment = configService.getEnvironmentName();
        final isConfigStaging = configService.isStaging;

        // Should use staging configuration
        expect(isConfigStaging, true, reason: 'Should be in staging mode');

        expect(
          environment,
          equals('Staging'),
          reason: 'Environment should be Staging',
        );

        // Should use staging URL or contain staging indicators
        expect(
          serverUrl.contains('dev.api.gameflex.io') || environment == 'Staging',
          true,
          reason: 'Should use staging configuration, got: $serverUrl',
        );

        // Should not use local URLs
        expect(
          serverUrl.contains('localhost') || serverUrl.contains('********'),
          false,
          reason: 'Staging should not use local URLs, got: $serverUrl',
        );
      },
    );

    test('Configuration should provide server URL options', () {
      final options = configService.getServerUrlOptions();

      expect(options, isNotEmpty, reason: 'Should have server URL options');

      // Should have staging option
      final hasStaging = options.any(
        (option) =>
            option.url.contains('dev.api.gameflex.io') ||
            option.name.contains('Staging'),
      );
      expect(hasStaging, isTrue, reason: 'Should have staging option');

      // Should have production option
      final hasProduction = options.any(
        (option) =>
            option.url.contains('gameflex.io') && !option.url.contains('dev.'),
      );
      expect(hasProduction, isTrue, reason: 'Should have production option');
    });
  });
}
